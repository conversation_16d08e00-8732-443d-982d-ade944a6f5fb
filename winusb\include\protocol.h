#pragma once
#include <windows.h>

#pragma pack(push, 1)

// 网络DMA协议头部
typedef struct _NETWORK_DMA_HEADER {
    DWORD dwMagic;              // 0xDEADBEEF
    DWORD dwVersion;            // 协议版本
    DWORD dwCommand;            // 命令类型
    DWORD dwSequence;           // 序列号
    QWORD qwTimestamp;          // 时间戳
    DWORD dwDataLength;         // 数据长度
    DWORD dwChecksum;           // 校验和
} NETWORK_DMA_HEADER, *PNETWORK_DMA_HEADER;

// 虚拟内存读取请求 (替代物理地址)
typedef struct _NETWORK_DMA_VIRT_READ_REQUEST {
    NETWORK_DMA_HEADER header;
    DWORD dwProcessId;          // 目标进程ID
    QWORD qwVirtualAddress;     // 虚拟地址
    DWORD dwLength;             // 读取长度
    DWORD dwFlags;              // 标志位
    UCHAR ucPipeID;             // USB管道ID (保持兼容性)
    UCHAR ucReserved[3];        // 对齐
} NETWORK_DMA_VIRT_READ_REQUEST, *PNETWORK_DMA_VIRT_READ_REQUEST;

// Scatter读取条目
typedef struct _SCATTER_ENTRY {
    QWORD qwVirtualAddress;     // 虚拟地址
    DWORD dwLength;             // 长度
    DWORD dwOffset;             // 在输出缓冲区中的偏移
} SCATTER_ENTRY, *PSCATTER_ENTRY;

// Scatter读取请求
typedef struct _NETWORK_DMA_SCATTER_REQUEST {
    NETWORK_DMA_HEADER header;
    DWORD dwProcessId;          // 目标进程ID
    DWORD dwEntryCount;         // scatter条目数量
    DWORD dwTotalBufferSize;    // 总缓冲区大小
    UCHAR ucPipeID;             // USB管道ID
    UCHAR ucReserved[3];        // 对齐
    // 后跟dwEntryCount个SCATTER_ENTRY
} NETWORK_DMA_SCATTER_REQUEST, *PNETWORK_DMA_SCATTER_REQUEST;

// 虚拟内存写入请求
typedef struct _NETWORK_DMA_VIRT_WRITE_REQUEST {
    NETWORK_DMA_HEADER header;
    DWORD dwProcessId;          // 目标进程ID
    QWORD qwVirtualAddress;     // 虚拟地址
    DWORD dwLength;             // 写入长度
    DWORD dwFlags;              // 标志位
    UCHAR ucPipeID;             // USB管道ID
    UCHAR ucReserved[3];        // 对齐
    // 后跟dwLength字节的数据
} NETWORK_DMA_VIRT_WRITE_REQUEST, *PNETWORK_DMA_VIRT_WRITE_REQUEST;

// CSR寄存器写入请求 (保持不变)
typedef struct _NETWORK_DMA_CSR_WRITE {
    NETWORK_DMA_HEADER header;
    WORD wRegAddr;              // 寄存器地址
    DWORD dwRegValue;           // 寄存器值
    BYTE fCSR;                  // CSR标志
    UCHAR ucReserved[3];        // 对齐
} NETWORK_DMA_CSR_WRITE, *PNETWORK_DMA_CSR_WRITE;

// CSR寄存器读取请求
typedef struct _NETWORK_DMA_CSR_READ {
    NETWORK_DMA_HEADER header;
    WORD wRegAddr;              // 寄存器地址
    BYTE fCSR;                  // CSR标志
    UCHAR ucReserved[5];        // 对齐
} NETWORK_DMA_CSR_READ, *PNETWORK_DMA_CSR_READ;

// 响应数据包
typedef struct _NETWORK_DMA_RESPONSE {
    NETWORK_DMA_HEADER header;
    DWORD dwStatus;             // 状态码 (0=成功)
    DWORD dwErrorCode;          // 错误代码
    BYTE bData[1];              // 可变长度数据
} NETWORK_DMA_RESPONSE, *PNETWORK_DMA_RESPONSE;

#pragma pack(pop)

// 命令类型定义
#define NETWORK_DMA_CMD_READ_MEMORY     0x0001  // 物理内存读取 (已废弃)
#define NETWORK_DMA_CMD_WRITE_MEMORY    0x0002  // 物理内存写入 (已废弃)
#define NETWORK_DMA_CMD_CSR_READ        0x0003  // CSR寄存器读取
#define NETWORK_DMA_CMD_CSR_WRITE       0x0004  // CSR寄存器写入
#define NETWORK_DMA_CMD_PING            0x0005  // 连接测试
#define NETWORK_DMA_CMD_DISCONNECT      0x0006  // 断开连接
#define NETWORK_DMA_CMD_READ_VIRTUAL    0x0007  // 虚拟内存读取
#define NETWORK_DMA_CMD_SCATTER_READ    0x0008  // Scatter读取
#define NETWORK_DMA_CMD_WRITE_VIRTUAL   0x0009  // 虚拟内存写入

// USB管道ID映射 (保持与原始代码兼容)
#define USB_EP_DMAIN1               0x81
#define USB_EP_DMAIN2               0x82
#define USB_EP_DMAIN3               0x83
#define USB_EP_DMAOUT               0x02
#define USB_EP_CSRIN                0x84
#define USB_EP_CSROUT               0x04

// 协议常量
#define NETWORK_DMA_MAGIC           0xDEADBEEF
#define NETWORK_DMA_RESPONSE_MAGIC  0xBEEFDEAD
#define NETWORK_DMA_VERSION         2           // 版本2支持虚拟地址
#define NETWORK_DMA_MAX_SCATTER     64          // 最大scatter条目数

// 状态码定义
#define NETWORK_DMA_STATUS_SUCCESS          0x00000000
#define NETWORK_DMA_STATUS_INVALID_REQUEST  0x00000001
#define NETWORK_DMA_STATUS_ACCESS_DENIED    0x00000002
#define NETWORK_DMA_STATUS_PROCESS_NOT_FOUND 0x00000003
#define NETWORK_DMA_STATUS_MEMORY_ERROR     0x00000004
#define NETWORK_DMA_STATUS_NETWORK_ERROR    0x00000005
#define NETWORK_DMA_STATUS_TIMEOUT          0x00000006

// 函数声明
DWORD CalculateChecksum(PBYTE pData, DWORD dwLength);
BOOL ValidateHeader(PNETWORK_DMA_HEADER pHeader, DWORD dwExpectedCommand);
