#include "../include/address_manager.h"
#include <psapi.h>
#include <tlhelp32.h>

#pragma comment(lib, "psapi.lib")

// 全局地址管理器实例
AddressManager* g_pAddressManager = nullptr;

AddressManager::AddressManager() {
    InitializeCriticalSection(&m_lock);
    
    // 初始化缓存
    ZeroMemory(m_cache, sizeof(m_cache));
    m_dwCacheIndex = 0;
    
    // 初始化DMA上下文
    ZeroMemory(&m_dmaContext, sizeof(m_dmaContext));
    m_bContextValid = FALSE;
    
    // 初始化scatter重组
    ZeroMemory(m_scatterReorg, sizeof(m_scatterReorg));
    m_dwScatterCount = 0;
    
    // 初始化进程句柄缓存
    ZeroMemory(m_processHandles, sizeof(m_processHandles));
    m_dwProcessHandleCount = 0;
}

AddressManager::~AddressManager() {
    Cleanup();
    DeleteCriticalSection(&m_lock);
}

BOOL AddressManager::Initialize() {
    EnterCriticalSection(&m_lock);
    
    // 清理旧的缓存
    CleanupCache();
    CleanupProcessHandles();
    
    LeaveCriticalSection(&m_lock);
    return TRUE;
}

VOID AddressManager::Cleanup() {
    EnterCriticalSection(&m_lock);
    
    CleanupProcessHandles();
    CleanupCache();
    ClearDMAContext();
    
    LeaveCriticalSection(&m_lock);
}

VOID AddressManager::SetDMAContext(DWORD dwProcessId, QWORD qwBaseAddress, 
                                  DWORD dwLength, UCHAR ucPipeID) {
    EnterCriticalSection(&m_lock);
    
    m_dmaContext.dwProcessId = dwProcessId;
    m_dmaContext.qwBaseAddress = qwBaseAddress;
    m_dmaContext.dwTotalLength = dwLength;
    m_dmaContext.ucPipeID = ucPipeID;
    m_dmaContext.bScatterMode = FALSE;
    m_bContextValid = TRUE;
    
    LeaveCriticalSection(&m_lock);
}

VOID AddressManager::ClearDMAContext() {
    EnterCriticalSection(&m_lock);
    
    ZeroMemory(&m_dmaContext, sizeof(m_dmaContext));
    m_bContextValid = FALSE;
    
    LeaveCriticalSection(&m_lock);
}

BOOL AddressManager::GetDMAContext(PDMA_CONTEXT pContext) {
    if (!pContext) return FALSE;
    
    EnterCriticalSection(&m_lock);
    
    if (m_bContextValid) {
        memcpy(pContext, &m_dmaContext, sizeof(DMA_CONTEXT));
    }
    
    BOOL bResult = m_bContextValid;
    LeaveCriticalSection(&m_lock);
    
    return bResult;
}

BOOL AddressManager::VirtualToPhysical(DWORD dwProcessId, QWORD qwVirtualAddr, 
                                      PQWORD pqwPhysicalAddr) {
    if (!pqwPhysicalAddr) return FALSE;
    
    EnterCriticalSection(&m_lock);
    
    // 首先检查缓存
    if (GetFromCache(dwProcessId, qwVirtualAddr, pqwPhysicalAddr)) {
        LeaveCriticalSection(&m_lock);
        return TRUE;
    }
    
    // 获取进程句柄
    HANDLE hProcess = GetProcessHandle(dwProcessId);
    if (!hProcess) {
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    // 执行地址转换
    QWORD qwPhysicalAddr = 0;
    BOOL bResult = TranslateVirtualToPhysical(hProcess, qwVirtualAddr, &qwPhysicalAddr);
    
    if (bResult) {
        *pqwPhysicalAddr = qwPhysicalAddr;
        // 缓存结果
        CacheVirtualToPhysical(dwProcessId, qwVirtualAddr, qwPhysicalAddr);
    }
    
    LeaveCriticalSection(&m_lock);
    return bResult;
}

HANDLE AddressManager::GetProcessHandle(DWORD dwProcessId) {
    // 查找现有句柄
    for (DWORD i = 0; i < m_dwProcessHandleCount; i++) {
        if (m_processHandles[i].dwProcessId == dwProcessId) {
            m_processHandles[i].dwLastAccess = GetTickCount();
            return m_processHandles[i].hProcess;
        }
    }
    
    // 打开新的进程句柄
    HANDLE hProcess = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, 
                                 FALSE, dwProcessId);
    if (!hProcess) {
        return NULL;
    }
    
    // 添加到缓存
    DWORD dwSlot = GetFreeProcessHandleSlot();
    if (dwSlot < MAX_PROCESS_HANDLES) {
        m_processHandles[dwSlot].dwProcessId = dwProcessId;
        m_processHandles[dwSlot].hProcess = hProcess;
        m_processHandles[dwSlot].dwLastAccess = GetTickCount();
        
        if (dwSlot >= m_dwProcessHandleCount) {
            m_dwProcessHandleCount = dwSlot + 1;
        }
    }
    
    return hProcess;
}

VOID AddressManager::CloseProcessHandle(DWORD dwProcessId) {
    for (DWORD i = 0; i < m_dwProcessHandleCount; i++) {
        if (m_processHandles[i].dwProcessId == dwProcessId) {
            if (m_processHandles[i].hProcess) {
                CloseHandle(m_processHandles[i].hProcess);
            }
            ZeroMemory(&m_processHandles[i], sizeof(m_processHandles[i]));
            break;
        }
    }
}

VOID AddressManager::CleanupProcessHandles() {
    for (DWORD i = 0; i < m_dwProcessHandleCount; i++) {
        if (m_processHandles[i].hProcess) {
            CloseHandle(m_processHandles[i].hProcess);
        }
    }
    ZeroMemory(m_processHandles, sizeof(m_processHandles));
    m_dwProcessHandleCount = 0;
}

BOOL AddressManager::CacheVirtualToPhysical(DWORD dwProcessId, QWORD qwVirtualAddr, 
                                           QWORD qwPhysicalAddr) {
    DWORD dwIndex = m_dwCacheIndex % CACHE_SIZE;
    
    m_cache[dwIndex].dwProcessId = dwProcessId;
    m_cache[dwIndex].qwVirtualAddress = qwVirtualAddr & ~0xFFF; // 页对齐
    m_cache[dwIndex].qwPhysicalAddress = qwPhysicalAddr & ~0xFFF; // 页对齐
    m_cache[dwIndex].dwTimestamp = GetTickCount();
    m_cache[dwIndex].bValid = TRUE;
    
    m_dwCacheIndex++;
    return TRUE;
}

BOOL AddressManager::GetFromCache(DWORD dwProcessId, QWORD qwVirtualAddr, 
                                 PQWORD pqwPhysicalAddr) {
    QWORD qwPageAddr = qwVirtualAddr & ~0xFFF;
    DWORD dwOffset = (DWORD)(qwVirtualAddr & 0xFFF);
    
    for (DWORD i = 0; i < CACHE_SIZE; i++) {
        if (IsValidCacheEntry(&m_cache[i], dwProcessId, qwPageAddr)) {
            *pqwPhysicalAddr = m_cache[i].qwPhysicalAddress + dwOffset;
            return TRUE;
        }
    }
    
    return FALSE;
}

BOOL AddressManager::IsValidCacheEntry(PADDRESS_CACHE_ENTRY pEntry, 
                                      DWORD dwProcessId, QWORD qwVirtualAddr) {
    if (!pEntry->bValid) return FALSE;
    if (pEntry->dwProcessId != dwProcessId) return FALSE;
    if (pEntry->qwVirtualAddress != qwVirtualAddr) return FALSE;
    
    // 检查缓存是否过期 (5秒)
    DWORD dwCurrentTime = GetTickCount();
    if (dwCurrentTime - pEntry->dwTimestamp > 5000) {
        pEntry->bValid = FALSE;
        return FALSE;
    }
    
    return TRUE;
}

VOID AddressManager::InvalidateCache(DWORD dwProcessId) {
    EnterCriticalSection(&m_lock);
    
    for (DWORD i = 0; i < CACHE_SIZE; i++) {
        if (dwProcessId == 0 || m_cache[i].dwProcessId == dwProcessId) {
            m_cache[i].bValid = FALSE;
        }
    }
    
    LeaveCriticalSection(&m_lock);
}

VOID AddressManager::CleanupCache() {
    ZeroMemory(m_cache, sizeof(m_cache));
    m_dwCacheIndex = 0;
}

DWORD AddressManager::GetCacheHitRate() {
    // 简单的缓存命中率统计
    DWORD dwValidEntries = 0;
    for (DWORD i = 0; i < CACHE_SIZE; i++) {
        if (m_cache[i].bValid) {
            dwValidEntries++;
        }
    }
    return (dwValidEntries * 100) / CACHE_SIZE;
}

DWORD AddressManager::GetFreeProcessHandleSlot() {
    // 查找空闲槽位
    for (DWORD i = 0; i < MAX_PROCESS_HANDLES; i++) {
        if (m_processHandles[i].dwProcessId == 0) {
            return i;
        }
    }
    
    // 查找最旧的槽位
    DWORD dwOldestSlot = 0;
    DWORD dwOldestTime = m_processHandles[0].dwLastAccess;
    
    for (DWORD i = 1; i < MAX_PROCESS_HANDLES; i++) {
        if (m_processHandles[i].dwLastAccess < dwOldestTime) {
            dwOldestTime = m_processHandles[i].dwLastAccess;
            dwOldestSlot = i;
        }
    }
    
    // 关闭旧句柄
    if (m_processHandles[dwOldestSlot].hProcess) {
        CloseHandle(m_processHandles[dwOldestSlot].hProcess);
        ZeroMemory(&m_processHandles[dwOldestSlot], sizeof(m_processHandles[dwOldestSlot]));
    }
    
    return dwOldestSlot;
}

// 全局函数实现
BOOL InitializeAddressManager() {
    if (g_pAddressManager) return TRUE;
    
    g_pAddressManager = new AddressManager();
    if (!g_pAddressManager) return FALSE;
    
    return g_pAddressManager->Initialize();
}

VOID CleanupAddressManager() {
    if (g_pAddressManager) {
        delete g_pAddressManager;
        g_pAddressManager = nullptr;
    }
}

// 地址转换实现
BOOL AddressManager::TranslateVirtualToPhysical(HANDLE hProcess, QWORD qwVirtualAddr,
                                               PQWORD pqwPhysicalAddr) {
    if (!hProcess || !pqwPhysicalAddr) return FALSE;

    // 在Windows用户模式下，我们无法直接获取物理地址
    // 这里我们返回虚拟地址，让主机端使用mmcopyvirtualmemory处理
    // 实际的物理地址转换需要在内核模式下进行

    MEMORY_BASIC_INFORMATION mbi;
    if (!ReadProcessMemoryInfo(hProcess, qwVirtualAddr, &mbi)) {
        return FALSE;
    }

    // 检查内存是否可访问
    if (!(mbi.State & MEM_COMMIT) || (mbi.Protect & PAGE_NOACCESS)) {
        return FALSE;
    }

    // 对于用户模式实现，我们直接返回虚拟地址
    // 主机端将使用进程ID和虚拟地址进行mmcopyvirtualmemory调用
    *pqwPhysicalAddr = qwVirtualAddr;
    return TRUE;
}

BOOL AddressManager::ReadProcessMemoryInfo(HANDLE hProcess, QWORD qwVirtualAddr,
                                          PMEMORY_BASIC_INFORMATION pMBI) {
    if (!hProcess || !pMBI) return FALSE;

    SIZE_T dwResult = VirtualQueryEx(hProcess, (LPCVOID)qwVirtualAddr, pMBI, sizeof(MEMORY_BASIC_INFORMATION));
    return dwResult == sizeof(MEMORY_BASIC_INFORMATION);
}

// Scatter处理实现
BOOL AddressManager::PrepareScatterRequest(DWORD dwProcessId, PSCATTER_ENTRY pEntries,
                                          DWORD dwEntryCount, PSCATTER_ENTRY pReorganized,
                                          PDWORD pdwReorganizedCount) {
    if (!pEntries || !pReorganized || !pdwReorganizedCount || dwEntryCount == 0) {
        return FALSE;
    }

    EnterCriticalSection(&m_lock);

    // 清空重组缓存
    m_dwScatterCount = 0;

    // 处理每个scatter条目
    DWORD dwReorganizedCount = 0;
    for (DWORD i = 0; i < dwEntryCount && i < MAX_SCATTER_ENTRIES; i++) {
        QWORD qwPhysicalAddr = 0;

        // 转换虚拟地址到物理地址
        if (VirtualToPhysical(dwProcessId, pEntries[i].qwVirtualAddress, &qwPhysicalAddr)) {
            // 保存重组信息
            m_scatterReorg[m_dwScatterCount].qwOriginalVirtAddr = pEntries[i].qwVirtualAddress;
            m_scatterReorg[m_dwScatterCount].qwPhysicalAddr = qwPhysicalAddr;
            m_scatterReorg[m_dwScatterCount].dwLength = pEntries[i].dwLength;
            m_scatterReorg[m_dwScatterCount].dwOriginalOffset = pEntries[i].dwOffset;
            m_scatterReorg[m_dwScatterCount].dwNewOffset = dwReorganizedCount * 4096; // 简化处理
            m_dwScatterCount++;

            // 创建重组后的条目
            pReorganized[dwReorganizedCount].qwVirtualAddress = pEntries[i].qwVirtualAddress;
            pReorganized[dwReorganizedCount].dwLength = pEntries[i].dwLength;
            pReorganized[dwReorganizedCount].dwOffset = pEntries[i].dwOffset;
            dwReorganizedCount++;
        }
    }

    *pdwReorganizedCount = dwReorganizedCount;

    LeaveCriticalSection(&m_lock);
    return dwReorganizedCount > 0;
}

BOOL AddressManager::ReorganizeScatterResponse(PBYTE pSourceBuffer, DWORD dwSourceSize,
                                              PBYTE pTargetBuffer, DWORD dwTargetSize) {
    if (!pSourceBuffer || !pTargetBuffer || m_dwScatterCount == 0) {
        return FALSE;
    }

    EnterCriticalSection(&m_lock);

    // 重新组织响应数据到原始偏移位置
    for (DWORD i = 0; i < m_dwScatterCount; i++) {
        PSCATTER_REORG_ENTRY pEntry = &m_scatterReorg[i];

        // 检查边界
        if (pEntry->dwNewOffset + pEntry->dwLength <= dwSourceSize &&
            pEntry->dwOriginalOffset + pEntry->dwLength <= dwTargetSize) {

            memcpy(pTargetBuffer + pEntry->dwOriginalOffset,
                   pSourceBuffer + pEntry->dwNewOffset,
                   pEntry->dwLength);
        }
    }

    LeaveCriticalSection(&m_lock);
    return TRUE;
}

// Scatter优化实现
BOOL AddressManager::OptimizeScatterEntries(PSCATTER_ENTRY pEntries, DWORD dwEntryCount,
                                           PSCATTER_ENTRY pOptimized, PDWORD pdwOptimizedCount) {
    if (!pEntries || !pOptimized || !pdwOptimizedCount || dwEntryCount == 0) {
        return FALSE;
    }

    // 首先按地址排序
    for (DWORD i = 0; i < dwEntryCount - 1; i++) {
        for (DWORD j = i + 1; j < dwEntryCount; j++) {
            if (pEntries[i].qwVirtualAddress > pEntries[j].qwVirtualAddress) {
                SCATTER_ENTRY temp = pEntries[i];
                pEntries[i] = pEntries[j];
                pEntries[j] = temp;
            }
        }
    }

    // 合并连续的条目
    return MergeContiguousEntries(pEntries, dwEntryCount, pOptimized, pdwOptimizedCount);
}

BOOL AddressManager::MergeContiguousEntries(PSCATTER_ENTRY pEntries, DWORD dwEntryCount,
                                           PSCATTER_ENTRY pMerged, PDWORD pdwMergedCount) {
    if (!pEntries || !pMerged || !pdwMergedCount || dwEntryCount == 0) {
        return FALSE;
    }

    DWORD dwMergedCount = 0;
    pMerged[0] = pEntries[0];
    dwMergedCount = 1;

    for (DWORD i = 1; i < dwEntryCount; i++) {
        PSCATTER_ENTRY pCurrent = &pEntries[i];
        PSCATTER_ENTRY pLast = &pMerged[dwMergedCount - 1];

        // 检查是否可以合并
        if (pLast->qwVirtualAddress + pLast->dwLength == pCurrent->qwVirtualAddress) {
            // 合并条目
            pLast->dwLength += pCurrent->dwLength;
        } else {
            // 添加新条目
            if (dwMergedCount < MAX_SCATTER_ENTRIES) {
                pMerged[dwMergedCount] = *pCurrent;
                dwMergedCount++;
            }
        }
    }

    *pdwMergedCount = dwMergedCount;
    return TRUE;
}

VOID AddressManager::CleanupOldProcessHandles() {
    DWORD dwCurrentTime = GetTickCount();

    for (DWORD i = 0; i < m_dwProcessHandleCount; i++) {
        // 清理超过30秒未使用的句柄
        if (m_processHandles[i].hProcess &&
            dwCurrentTime - m_processHandles[i].dwLastAccess > 30000) {
            CloseHandle(m_processHandles[i].hProcess);
            ZeroMemory(&m_processHandles[i], sizeof(m_processHandles[i]));
        }
    }
}
