@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WinUSB DLL劫持部署脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限来部署到系统目录
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置路径
set "SYSTEM_DIR=C:\Windows\System32"
set "BUILD_DIR=%~dp0build\bin"
set "BACKUP_DIR=%~dp0backup"

:: 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

echo 1. 检查构建文件...
if not exist "%BUILD_DIR%\winusb.dll" (
    echo 错误: 找不到构建的winusb.dll文件
    echo 请先运行构建脚本
    pause
    exit /b 1
)

echo    ✓ 找到winusb.dll

echo.
echo 2. 备份原始文件...
if exist "%SYSTEM_DIR%\winusb.dll" (
    copy "%SYSTEM_DIR%\winusb.dll" "%BACKUP_DIR%\winusb_original.dll" >nul
    if !errorLevel! equ 0 (
        echo    ✓ 原始winusb.dll已备份到 %BACKUP_DIR%
    ) else (
        echo    ✗ 备份失败
        pause
        exit /b 1
    )
) else (
    echo    ! 系统中未找到原始winusb.dll
)

echo.
echo 3. 部署劫持DLL...
copy "%BUILD_DIR%\winusb.dll" "%SYSTEM_DIR%\winusb.dll" >nul
if %errorLevel% equ 0 (
    echo    ✓ 劫持DLL部署成功
) else (
    echo    ✗ 部署失败
    pause
    exit /b 1
)

echo.
echo 4. 验证部署...
if exist "%SYSTEM_DIR%\winusb.dll" (
    echo    ✓ 文件存在于系统目录
    
    :: 检查文件版本
    powershell -Command "(Get-Item '%SYSTEM_DIR%\winusb.dll').VersionInfo.FileVersion" 2>nul
    if !errorLevel! equ 0 (
        echo    ✓ 文件版本信息正常
    )
) else (
    echo    ✗ 验证失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 部署完成!
echo ========================================
echo.
echo 重要提醒:
echo 1. 请确保主机端DMA服务器正在运行
echo 2. 检查网络配置 (默认: 192.168.1.100:28473)
echo 3. 查看日志文件: %%TEMP%%\winusb_hijack.log
echo 4. 如需还原, 运行 restore.bat
echo.

:: 创建还原脚本
echo @echo off > restore.bat
echo echo 还原原始winusb.dll... >> restore.bat
echo net session ^>nul 2^>^&1 >> restore.bat
echo if %%errorLevel%% neq 0 ^( >> restore.bat
echo     echo 错误: 需要管理员权限 >> restore.bat
echo     pause >> restore.bat
echo     exit /b 1 >> restore.bat
echo ^) >> restore.bat
echo if exist "%BACKUP_DIR%\winusb_original.dll" ^( >> restore.bat
echo     copy "%BACKUP_DIR%\winusb_original.dll" "%SYSTEM_DIR%\winusb.dll" ^>nul >> restore.bat
echo     echo 原始文件已还原 >> restore.bat
echo ^) else ^( >> restore.bat
echo     del "%SYSTEM_DIR%\winusb.dll" ^>nul 2^>^&1 >> restore.bat
echo     echo 劫持DLL已删除 >> restore.bat
echo ^) >> restore.bat
echo pause >> restore.bat

echo 还原脚本已创建: restore.bat
echo.
pause
