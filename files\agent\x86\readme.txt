The LeechAgent have dependencies on external software for memory capture which should be placed in this folder if needed.

32-bit LeechAgent does not support Python or MemProcFS functionality. It however supports memory acquisition via driver!

Recommended for (default) service mode: WinPMEM:
https://github.com/Velocidex/WinPmem/raw/master/kernel/binaries/winpmem_x86.sys

Recommended for interactive mode: DumpIt:
https://www.comae.com
