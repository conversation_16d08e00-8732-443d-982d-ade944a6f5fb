<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}</ProjectGuid>
    <RootNamespace>leechagent</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\agent\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\agent\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\agent\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\agent\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>leechcore.lib;Rpcrt4.lib;Secur32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
    <PreBuildEvent>
      <Command>
copy "$(SolutionDir)leechcore\leechrpc.h" "$(ProjectDir)" /y

copy "$(SolutionDir)leechcore\leechrpcshared.c" "$(ProjectDir)" /y
</Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>None</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>Stub</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)files\leechcore.dll" "$(OutDir)" /y
copy "$(SolutionDir)files\leechcorepyc.pyd" "$(OutDir)" /y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>leechcore.lib;Rpcrt4.lib;Secur32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
    <PreBuildEvent>
      <Command>
copy "$(SolutionDir)leechcore\leechrpc.h" "$(ProjectDir)" /y

copy "$(SolutionDir)leechcore\leechrpcshared.c" "$(ProjectDir)" /y
</Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>None</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>Stub</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)files\x86\leechcore.dll" "$(OutDir)" /y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>leechcore.lib;Rpcrt4.lib;Secur32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
    <PreBuildEvent>
      <Command>
copy "$(SolutionDir)leechcore\leechrpc.h" "$(ProjectDir)" /y

copy "$(SolutionDir)leechcore\leechrpcshared.c" "$(ProjectDir)" /y
</Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>None</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>Stub</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)files\leechcore.dll" "$(OutDir)" /y
copy "$(SolutionDir)files\leechcorepyc.pyd" "$(OutDir)" /y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>leechcore.lib;Rpcrt4.lib;Secur32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
    <PreBuildEvent>
      <Command>
copy "$(SolutionDir)leechcore\leechrpc.h" "$(ProjectDir)" /y

copy "$(SolutionDir)leechcore\leechrpcshared.c" "$(ProjectDir)" /y
</Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>None</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>Stub</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)files\x86\leechcore.dll" "$(OutDir)" /y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ResourceCompile Include="leechagent.rc" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="leechrpc.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="leechagent.c" />
    <ClCompile Include="leechagent_procchild.c" />
    <ClCompile Include="leechagent_procparent.c" />
    <ClCompile Include="leechagent_rpc.c" />
    <ClCompile Include="leechagent_svc.c" />
    <ClCompile Include="leechrpcserver.c" />
    <ClCompile Include="leechrpcshared.c" />
    <ClCompile Include="leechrpc_s.c" />
    <ClCompile Include="util.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\includes\leechcore.h" />
    <ClInclude Include="leechagent.h" />
    <ClInclude Include="leechagent_proc.h" />
    <ClInclude Include="leechagent_rpc.h" />
    <ClInclude Include="leechagent_svc.h" />
    <ClInclude Include="leechrpc.h" />
    <ClInclude Include="leechrpc_h.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="version.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>