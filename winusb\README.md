# WinUSB DLL劫持实现

## 概述

这是一个完整的WinUSB DLL劫持实现，用于将LeechCore的硬件DMA读取转换为网络请求。该实现支持虚拟地址访问，并保持与LeechCore scatter读取功能的兼容性。

## 技术架构

### 核心组件

1. **WinUSB劫持层** (`winusb_hooks.cpp`)
   - 劫持WinUSB API调用
   - 识别DMA和CSR操作
   - 转发非DMA操作到原始WinUSB.dll

2. **网络客户端** (`network_client.cpp`)
   - 管理与主机端的TCP连接
   - 实现协议通信
   - 处理虚拟内存读写请求

3. **地址管理器** (`address_manager.cpp`)
   - 虚拟到物理地址转换
   - 地址缓存机制
   - Scatter请求重组

4. **协议层** (`protocol.cpp`)
   - 定义网络DMA协议
   - 数据包验证和校验
   - 命令处理

## 虚拟地址对Scatter功能的影响分析

### 问题分析

LeechCore的scatter读取功能依赖于物理地址的连续性来优化批量读取。使用虚拟地址会带来以下影响：

1. **地址不连续性**：虚拟地址在物理内存中可能不连续
2. **页面边界**：跨页面的读取需要特殊处理
3. **进程上下文**：需要指定目标进程ID

### 解决方案

#### 1. Scatter请求重组
```cpp
// 在address_manager.cpp中实现
BOOL PrepareScatterRequest(DWORD dwProcessId, PSCATTER_ENTRY pEntries, 
                          DWORD dwEntryCount, PSCATTER_ENTRY pReorganized, 
                          PDWORD pdwReorganizedCount);
```

- 将原始scatter请求按虚拟地址重新组织
- 保持原始偏移映射关系
- 优化连续虚拟地址的合并

#### 2. 地址缓存机制
```cpp
// 256条目的地址转换缓存
typedef struct _ADDRESS_CACHE_ENTRY {
    DWORD dwProcessId;
    QWORD qwVirtualAddress;
    QWORD qwPhysicalAddress;
    DWORD dwTimestamp;
    BOOL bValid;
} ADDRESS_CACHE_ENTRY;
```

- 缓存虚拟到物理地址的映射
- 5秒超时自动失效
- 按进程ID隔离缓存

#### 3. 响应数据重组
```cpp
BOOL ReorganizeScatterResponse(PBYTE pSourceBuffer, DWORD dwSourceSize,
                              PBYTE pTargetBuffer, DWORD dwTargetSize);
```

- 将网络响应数据重新组织到原始偏移位置
- 保持与LeechCore期望的数据布局一致

## 构建说明

### 环境要求
- Visual Studio 2019或更高版本
- CMake 3.16或更高版本
- Windows SDK 10.0或更高版本

### 构建步骤

1. **创建构建目录**
```cmd
mkdir build
cd build
```

2. **生成项目文件**
```cmd
cmake .. -G "Visual Studio 16 2019" -A x64
```

3. **编译项目**
```cmd
cmake --build . --config Release
```

### 输出文件
- `bin/winusb.dll` - 劫持DLL
- `lib/winusb.lib` - 导入库

## 部署说明

### 1. 备份原始文件
```cmd
copy C:\Windows\System32\winusb.dll C:\Windows\System32\winusb_original.dll
```

### 2. 部署劫持DLL
```cmd
copy bin\winusb.dll C:\Windows\System32\winusb.dll
```

### 3. 配置网络设置
修改网络客户端中的服务器IP和端口：
```cpp
// 在network_client.cpp中
strcpy_s(m_szServerIP, sizeof(m_szServerIP), "*************");
m_wServerPort = 28473;
```

## 主机端要求

主机端需要实现支持虚拟地址的DMA服务器：

1. **支持mmcopyvirtualmemory API**
2. **处理NETWORK_DMA_CMD_READ_VIRTUAL命令**
3. **支持进程ID参数**
4. **实现scatter读取优化**

## 调试功能

### 日志输出
- 自动创建 `%TEMP%\winusb_hijack.log`
- 包含时间戳和详细操作信息
- 同时输出到调试器

### 性能监控
- 请求计数和错误率统计
- 网络传输字节统计
- 缓存命中率监控

## 注意事项

1. **管理员权限**：部署到系统目录需要管理员权限
2. **系统稳定性**：错误的实现可能导致系统不稳定
3. **兼容性**：确保与目标LeechCore版本兼容
4. **安全性**：网络通信应考虑加密和认证

## 故障排除

### 常见问题

1. **DLL加载失败**
   - 检查依赖库是否存在
   - 验证目标架构匹配（x64/x86）

2. **网络连接失败**
   - 检查服务器IP和端口配置
   - 验证防火墙设置

3. **地址转换错误**
   - 检查进程权限
   - 验证虚拟地址有效性

### 调试步骤

1. 检查日志文件内容
2. 使用Process Monitor监控文件/注册表访问
3. 使用Wireshark监控网络通信
4. 使用调试器附加到目标进程

## 版本历史

- v1.0.0 - 初始实现
  - 基本WinUSB劫持功能
  - 虚拟地址支持
  - Scatter读取兼容性
