The LeechAgent have dependencies on external software for memory capture which should be placed in this folder if required.

Please check out the Guide for detailed up-to-date information:
https://github.com/ufrisk/LeechCore/wiki/LeechAgent
https://github.com/ufrisk/LeechCore/wiki/LeechAgent_Install


Also there is a depencency on embedded Python - please check the Python sub-folder for information.

Recommended for (default) service mode: WinPMEM:
https://github.com/Velocidex/WinPmem/raw/master/kernel/binaries/winpmem_x64.sys

Recommended for interactive mode: DumpIt:
https://www.comae.com

---

If connecting to FPGA devices on the remote system 'FTD3XX.dll' is required.
If opening Hyper-V save files on the remote system 'vmsavedstatedumpprovider.dll' is required.
