#pragma once
#include <windows.h>
#include <winusb.h>

// LeechCore USB3380设备的数据结构 (从源码分析得出)
typedef struct _PIPE_SEND_CSR_WRITE {
    BYTE u1;                    // CSR标志 | 0x40
    BYTE u2;                    // 保留
    BYTE u3;                    // 寄存器地址低8位
    BYTE u4;                    // 寄存器地址高8位
    DWORD dwRegValue;           // 寄存器值
} PIPE_SEND_CSR_WRITE, *PPIPE_SEND_CSR_WRITE;

// DMA寄存器地址定义 (从LeechCore源码分析)
#define REG_DMAADDR_1           0x0100
#define REG_DMAADDR_2           0x0104
#define REG_DMAADDR_3           0x0108
#define REG_DMAADDR_4           0x010C
#define REG_DMALENGTH_1         0x0110
#define REG_DMALENGTH_2         0x0114
#define REG_DMALENGTH_3         0x0118
#define REG_DMALENGTH_4         0x011C

// 原始WinUSB函数指针类型定义
typedef BOOL (WINAPI *PFN_WinUsb_Initialize)(HANDLE DeviceHandle, PWINUSB_INTERFACE_HANDLE InterfaceHandle);
typedef BOOL (WINAPI *PFN_WinUsb_Free)(WINUSB_INTERFACE_HANDLE InterfaceHandle);
typedef BOOL (WINAPI *PFN_WinUsb_ReadPipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
typedef BOOL (WINAPI *PFN_WinUsb_WritePipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
typedef BOOL (WINAPI *PFN_WinUsb_SetPipePolicy)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, ULONG ValueLength, PVOID Value);
typedef BOOL (WINAPI *PFN_WinUsb_GetPipePolicy)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, PULONG ValueLength, PVOID Value);
typedef BOOL (WINAPI *PFN_WinUsb_ControlTransfer)(WINUSB_INTERFACE_HANDLE InterfaceHandle, WINUSB_SETUP_PACKET SetupPacket, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
typedef BOOL (WINAPI *PFN_WinUsb_ResetPipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);
typedef BOOL (WINAPI *PFN_WinUsb_AbortPipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);
typedef BOOL (WINAPI *PFN_WinUsb_FlushPipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);

// 原始函数指针
extern PFN_WinUsb_Initialize g_pfnOriginal_WinUsb_Initialize;
extern PFN_WinUsb_Free g_pfnOriginal_WinUsb_Free;
extern PFN_WinUsb_ReadPipe g_pfnOriginal_WinUsb_ReadPipe;
extern PFN_WinUsb_WritePipe g_pfnOriginal_WinUsb_WritePipe;
extern PFN_WinUsb_SetPipePolicy g_pfnOriginal_WinUsb_SetPipePolicy;
extern PFN_WinUsb_GetPipePolicy g_pfnOriginal_WinUsb_GetPipePolicy;
extern PFN_WinUsb_ControlTransfer g_pfnOriginal_WinUsb_ControlTransfer;
extern PFN_WinUsb_ResetPipe g_pfnOriginal_WinUsb_ResetPipe;
extern PFN_WinUsb_AbortPipe g_pfnOriginal_WinUsb_AbortPipe;
extern PFN_WinUsb_FlushPipe g_pfnOriginal_WinUsb_FlushPipe;

// 原始WinUSB模块句柄
extern HMODULE g_hOriginalWinUSB;

// DMA地址跟踪
typedef struct _DMA_ADDRESS_TRACKER {
    QWORD qwDMAAddress[4];      // 4个DMA通道的地址
    DWORD dwDMALength[4];       // 4个DMA通道的长度
    DWORD dwLastUpdateTime[4];  // 最后更新时间
    BOOL bValid[4];             // 是否有效
} DMA_ADDRESS_TRACKER, *PDMA_ADDRESS_TRACKER;

extern DMA_ADDRESS_TRACKER g_dmaTracker;
extern CRITICAL_SECTION g_dmaTrackerLock;

// 初始化和清理函数
BOOL LoadOriginalWinUSB();
VOID UnloadOriginalWinUSB();
BOOL InitializeHooks();
VOID CleanupHooks();

// 劫持函数声明
extern "C" {
    BOOL WINAPI WinUsb_Initialize(HANDLE DeviceHandle, PWINUSB_INTERFACE_HANDLE InterfaceHandle);
    BOOL WINAPI WinUsb_Free(WINUSB_INTERFACE_HANDLE InterfaceHandle);
    BOOL WINAPI WinUsb_ReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
    BOOL WINAPI WinUsb_WritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
    BOOL WINAPI WinUsb_SetPipePolicy(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, ULONG ValueLength, PVOID Value);
    BOOL WINAPI WinUsb_GetPipePolicy(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, PULONG ValueLength, PVOID Value);
    BOOL WINAPI WinUsb_ControlTransfer(WINUSB_INTERFACE_HANDLE InterfaceHandle, WINUSB_SETUP_PACKET SetupPacket, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
    BOOL WINAPI WinUsb_ResetPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);
    BOOL WINAPI WinUsb_AbortPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);
    BOOL WINAPI WinUsb_FlushPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID);
}

// 内部处理函数
BOOL HandleDMAReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                      PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred);
BOOL HandleDMAWritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                       PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred);
BOOL HandleCSRReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                      PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred);
BOOL HandleCSRWritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                       PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred);

// DMA地址跟踪函数
VOID UpdateDMAAddress(UCHAR ucChannel, QWORD qwAddress, DWORD dwLength);
QWORD GetDMAAddress(UCHAR ucPipeID);
DWORD GetDMALength(UCHAR ucPipeID);
UCHAR PipeIDToChannel(UCHAR ucPipeID);

// 工具函数
BOOL IsNetworkModeEnabled();
VOID EnableNetworkMode(BOOL bEnable);
DWORD GetCurrentProcessId();
