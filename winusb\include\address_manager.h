#pragma once
#include <windows.h>
#include "protocol.h"

// 地址转换缓存条目
typedef struct _ADDRESS_CACHE_ENTRY {
    DWORD dwProcessId;          // 进程ID
    QWORD qwVirtualAddress;     // 虚拟地址 (页对齐)
    QWORD qwPhysicalAddress;    // 物理地址 (页对齐)
    DWORD dwTimestamp;          // 缓存时间戳
    BOOL bValid;                // 是否有效
} ADDRESS_CACHE_ENTRY, *PADDRESS_CACHE_ENTRY;

// DMA上下文信息
typedef struct _DMA_CONTEXT {
    DWORD dwProcessId;          // 当前进程ID
    QWORD qwBaseAddress;        // 基础地址
    DWORD dwTotalLength;        // 总长度
    UCHAR ucPipeID;             // 管道ID
    BOOL bScatterMode;          // 是否为scatter模式
} DMA_CONTEXT, *PDMA_CONTEXT;

// Scatter重组信息
typedef struct _SCATTER_REORG_ENTRY {
    QWORD qwOriginalVirtAddr;   // 原始虚拟地址
    QWORD qwPhysicalAddr;       // 转换后的物理地址
    DWORD dwLength;             // 长度
    DWORD dwOriginalOffset;     // 原始偏移
    DWORD dwNewOffset;          // 重组后偏移
} SCATTER_REORG_ENTRY, *PSCATTER_REORG_ENTRY;

// 地址管理器类
class AddressManager {
private:
    CRITICAL_SECTION m_lock;
    
    // 地址转换缓存
    static const DWORD CACHE_SIZE = 256;
    ADDRESS_CACHE_ENTRY m_cache[CACHE_SIZE];
    DWORD m_dwCacheIndex;
    
    // DMA上下文跟踪
    DMA_CONTEXT m_dmaContext;
    BOOL m_bContextValid;
    
    // Scatter重组缓存
    static const DWORD MAX_SCATTER_ENTRIES = 64;
    SCATTER_REORG_ENTRY m_scatterReorg[MAX_SCATTER_ENTRIES];
    DWORD m_dwScatterCount;
    
    // 进程句柄缓存
    static const DWORD MAX_PROCESS_HANDLES = 32;
    struct {
        DWORD dwProcessId;
        HANDLE hProcess;
        DWORD dwLastAccess;
    } m_processHandles[MAX_PROCESS_HANDLES];
    DWORD m_dwProcessHandleCount;
    
public:
    AddressManager();
    ~AddressManager();
    
    // 初始化和清理
    BOOL Initialize();
    VOID Cleanup();
    
    // DMA上下文管理
    VOID SetDMAContext(DWORD dwProcessId, QWORD qwBaseAddress, DWORD dwLength, UCHAR ucPipeID);
    VOID ClearDMAContext();
    BOOL GetDMAContext(PDMA_CONTEXT pContext);
    
    // 地址转换
    BOOL VirtualToPhysical(DWORD dwProcessId, QWORD qwVirtualAddr, PQWORD pqwPhysicalAddr);
    BOOL PhysicalToVirtual(DWORD dwProcessId, QWORD qwPhysicalAddr, PQWORD pqwVirtualAddr);
    
    // Scatter处理
    BOOL PrepareScatterRequest(DWORD dwProcessId, PSCATTER_ENTRY pEntries, DWORD dwEntryCount,
                              PSCATTER_ENTRY pReorganized, PDWORD pdwReorganizedCount);
    BOOL ReorganizeScatterResponse(PBYTE pSourceBuffer, DWORD dwSourceSize,
                                  PBYTE pTargetBuffer, DWORD dwTargetSize);
    
    // 进程管理
    HANDLE GetProcessHandle(DWORD dwProcessId);
    VOID CloseProcessHandle(DWORD dwProcessId);
    VOID CleanupProcessHandles();
    
    // 缓存管理
    VOID InvalidateCache(DWORD dwProcessId = 0);
    VOID CleanupCache();
    DWORD GetCacheHitRate();
    
private:
    // 内部辅助函数
    BOOL CacheVirtualToPhysical(DWORD dwProcessId, QWORD qwVirtualAddr, QWORD qwPhysicalAddr);
    BOOL GetFromCache(DWORD dwProcessId, QWORD qwVirtualAddr, PQWORD pqwPhysicalAddr);
    DWORD GetCacheIndex(DWORD dwProcessId, QWORD qwVirtualAddr);
    BOOL IsValidCacheEntry(PADDRESS_CACHE_ENTRY pEntry, DWORD dwProcessId, QWORD qwVirtualAddr);
    
    // 地址转换实现
    BOOL TranslateVirtualToPhysical(HANDLE hProcess, QWORD qwVirtualAddr, PQWORD pqwPhysicalAddr);
    BOOL ReadProcessMemoryInfo(HANDLE hProcess, QWORD qwVirtualAddr, PMEMORY_BASIC_INFORMATION pMBI);
    
    // Scatter优化
    BOOL OptimizeScatterEntries(PSCATTER_ENTRY pEntries, DWORD dwEntryCount,
                               PSCATTER_ENTRY pOptimized, PDWORD pdwOptimizedCount);
    BOOL MergeContiguousEntries(PSCATTER_ENTRY pEntries, DWORD dwEntryCount,
                               PSCATTER_ENTRY pMerged, PDWORD pdwMergedCount);
    
    // 进程句柄管理
    DWORD FindProcessHandleSlot(DWORD dwProcessId);
    DWORD GetFreeProcessHandleSlot();
    VOID CleanupOldProcessHandles();
};

// 全局地址管理器实例
extern AddressManager* g_pAddressManager;

// 初始化和清理函数
BOOL InitializeAddressManager();
VOID CleanupAddressManager();
