@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WinUSB DLL劫持构建脚本
echo ========================================
echo.

:: 检查CMake
cmake --version >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 未找到CMake
    echo 请安装CMake并添加到PATH环境变量
    pause
    exit /b 1
)

:: 检查Visual Studio
where cl.exe >nul 2>&1
if %errorLevel% neq 0 (
    echo 正在查找Visual Studio...
    
    :: 尝试加载VS环境
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo 错误: 未找到Visual Studio
        echo 请安装Visual Studio 2019或2022
        pause
        exit /b 1
    )
)

echo ✓ 编译环境检查通过

:: 创建构建目录
if not exist build mkdir build
cd build

echo.
echo 1. 生成项目文件...
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorLevel% neq 0 (
    echo 尝试Visual Studio 2022...
    cmake .. -G "Visual Studio 17 2022" -A x64
    if !errorLevel! neq 0 (
        echo 错误: CMake配置失败
        pause
        exit /b 1
    )
)

echo    ✓ 项目文件生成成功

echo.
echo 2. 编译Release版本...
cmake --build . --config Release
if %errorLevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo    ✓ Release版本编译成功

echo.
echo 3. 编译Debug版本...
cmake --build . --config Debug
if %errorLevel% neq 0 (
    echo 警告: Debug版本编译失败，但Release版本可用
) else (
    echo    ✓ Debug版本编译成功
)

echo.
echo 4. 验证输出文件...
if exist "bin\Release\winusb.dll" (
    echo    ✓ Release DLL: bin\Release\winusb.dll
    
    :: 显示文件信息
    for %%F in ("bin\Release\winusb.dll") do (
        echo       大小: %%~zF 字节
        echo       时间: %%~tF
    )
) else (
    echo    ✗ Release DLL未找到
    pause
    exit /b 1
)

if exist "bin\Debug\winusb_d.dll" (
    echo    ✓ Debug DLL: bin\Debug\winusb_d.dll
)

:: 复制到统一的bin目录
if not exist "bin" mkdir bin
copy "bin\Release\winusb.dll" "bin\winusb.dll" >nul 2>&1

echo.
echo ========================================
echo 构建完成!
echo ========================================
echo.
echo 输出文件:
echo   Release: build\bin\Release\winusb.dll
echo   统一目录: build\bin\winusb.dll
echo.
echo 下一步:
echo   1. 运行 deploy.bat 部署到系统
echo   2. 或手动复制到目标程序目录
echo.

cd ..
pause
