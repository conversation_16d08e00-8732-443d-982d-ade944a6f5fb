#include <windows.h>
#include "../include/winusb_hooks.h"
#include "../include/network_client.h"
#include "../include/address_manager.h"
#include <stdio.h>

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            return OnProcessAttach(hModule);
            
        case DLL_THREAD_ATTACH:
            // 线程附加时不需要特殊处理
            break;
            
        case DLL_THREAD_DETACH:
            // 线程分离时不需要特殊处理
            break;
            
        case DLL_PROCESS_DETACH:
            OnProcessDetach();
            break;
    }
    return TRUE;
}

BOOL OnProcessAttach(HMODULE hModule) {
    // 禁用线程库调用以提高性能
    DisableThreadLibraryCalls(hModule);
    
    // 初始化调试输出
    InitializeDebugOutput();
    
    DebugPrint("WinUSB Hijack DLL loaded - Process: %d\r\n", GetCurrentProcessId());
    
    // 加载原始WinUSB.dll
    if (!LoadOriginalWinUSB()) {
        DebugPrint("Failed to load original winusb.dll\r\n");
        return FALSE;
    }
    
    DebugPrint("Original winusb.dll loaded successfully\r\n");
    
    // 初始化劫持系统
    if (!InitializeHooks()) {
        DebugPrint("Failed to initialize hooks\r\n");
        UnloadOriginalWinUSB();
        return FALSE;
    }
    
    DebugPrint("Hooks initialized successfully\r\n");
    
    // 初始化网络客户端
    if (!InitializeNetworkClient()) {
        DebugPrint("Failed to initialize network client\r\n");
        CleanupHooks();
        UnloadOriginalWinUSB();
        return FALSE;
    }
    
    DebugPrint("Network client initialized successfully\r\n");
    
    // 初始化地址管理器
    if (!InitializeAddressManager()) {
        DebugPrint("Failed to initialize address manager\r\n");
        CleanupNetworkClient();
        CleanupHooks();
        UnloadOriginalWinUSB();
        return FALSE;
    }
    
    DebugPrint("Address manager initialized successfully\r\n");
    
    // 尝试连接到服务器
    if (g_pNetworkClient && !g_pNetworkClient->Connect()) {
        DebugPrint("Warning: Failed to connect to DMA server, will retry later\r\n");
        // 不返回FALSE，允许程序继续运行，稍后重试连接
    } else {
        DebugPrint("Connected to DMA server successfully\r\n");
    }
    
    DebugPrint("WinUSB Hijack DLL initialization completed\r\n");
    return TRUE;
}

VOID OnProcessDetach() {
    DebugPrint("WinUSB Hijack DLL unloading - Process: %d\r\n", GetCurrentProcessId());
    
    // 清理地址管理器
    CleanupAddressManager();
    DebugPrint("Address manager cleaned up\r\n");
    
    // 清理网络客户端
    CleanupNetworkClient();
    DebugPrint("Network client cleaned up\r\n");
    
    // 清理劫持系统
    CleanupHooks();
    DebugPrint("Hooks cleaned up\r\n");
    
    // 卸载原始WinUSB.dll
    UnloadOriginalWinUSB();
    DebugPrint("Original winusb.dll unloaded\r\n");
    
    // 清理调试输出
    CleanupDebugOutput();
}

// 调试输出功能
static HANDLE g_hDebugFile = INVALID_HANDLE_VALUE;
static CRITICAL_SECTION g_debugLock;
static BOOL g_bDebugInitialized = FALSE;

VOID InitializeDebugOutput() {
    if (g_bDebugInitialized) return;
    
    InitializeCriticalSection(&g_debugLock);
    
    // 创建调试日志文件
    WCHAR szLogPath[MAX_PATH];
    GetTempPathW(MAX_PATH, szLogPath);
    wcscat_s(szLogPath, MAX_PATH, L"winusb_hijack.log");
    
    g_hDebugFile = CreateFileW(szLogPath, GENERIC_WRITE, FILE_SHARE_READ, 
                              nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, nullptr);
    
    g_bDebugInitialized = TRUE;
}

VOID CleanupDebugOutput() {
    if (!g_bDebugInitialized) return;
    
    if (g_hDebugFile != INVALID_HANDLE_VALUE) {
        CloseHandle(g_hDebugFile);
        g_hDebugFile = INVALID_HANDLE_VALUE;
    }
    
    DeleteCriticalSection(&g_debugLock);
    g_bDebugInitialized = FALSE;
}

VOID DebugPrint(const char* szFormat, ...) {
    if (!g_bDebugInitialized || g_hDebugFile == INVALID_HANDLE_VALUE) {
        return;
    }
    
    EnterCriticalSection(&g_debugLock);
    
    // 格式化消息
    char szBuffer[1024];
    va_list args;
    va_start(args, szFormat);
    vsnprintf_s(szBuffer, sizeof(szBuffer), _TRUNCATE, szFormat, args);
    va_end(args);
    
    // 添加时间戳
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    char szTimestamp[64];
    sprintf_s(szTimestamp, sizeof(szTimestamp), "[%04d-%02d-%02d %02d:%02d:%02d.%03d] ",
             st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
    
    // 写入文件
    DWORD dwBytesWritten;
    WriteFile(g_hDebugFile, szTimestamp, (DWORD)strlen(szTimestamp), &dwBytesWritten, nullptr);
    WriteFile(g_hDebugFile, szBuffer, (DWORD)strlen(szBuffer), &dwBytesWritten, nullptr);
    FlushFileBuffers(g_hDebugFile);
    
    // 同时输出到调试器
    OutputDebugStringA(szTimestamp);
    OutputDebugStringA(szBuffer);
    
    LeaveCriticalSection(&g_debugLock);
}

// 错误处理函数
VOID ReportError(const char* szFunction, DWORD dwError) {
    char szErrorMsg[256];
    FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                  nullptr, dwError, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                  szErrorMsg, sizeof(szErrorMsg), nullptr);
    
    DebugPrint("ERROR in %s: %d - %s\r\n", szFunction, dwError, szErrorMsg);
}

// 配置管理
BOOL LoadConfiguration() {
    // 从注册表或配置文件加载配置
    // 这里使用默认配置
    return TRUE;
}

VOID SaveConfiguration() {
    // 保存配置到注册表或配置文件
}

// 性能监控
static DWORD g_dwStartTime = 0;
static DWORD g_dwRequestCount = 0;
static DWORD g_dwErrorCount = 0;

VOID StartPerformanceMonitoring() {
    g_dwStartTime = GetTickCount();
    g_dwRequestCount = 0;
    g_dwErrorCount = 0;
}

VOID UpdatePerformanceCounters(BOOL bSuccess) {
    InterlockedIncrement(&g_dwRequestCount);
    if (!bSuccess) {
        InterlockedIncrement(&g_dwErrorCount);
    }
}

VOID ReportPerformanceStatistics() {
    DWORD dwElapsed = GetTickCount() - g_dwStartTime;
    if (dwElapsed > 0) {
        DWORD dwRequestsPerSecond = (g_dwRequestCount * 1000) / dwElapsed;
        DWORD dwErrorRate = g_dwErrorCount * 100 / max(g_dwRequestCount, 1);
        
        DebugPrint("Performance: %d requests in %d ms (%d req/s), %d%% error rate\r\n",
                  g_dwRequestCount, dwElapsed, dwRequestsPerSecond, dwErrorRate);
    }
}

// 健康检查
BOOL PerformHealthCheck() {
    BOOL bHealthy = TRUE;
    
    // 检查网络连接
    if (!g_pNetworkClient || !g_pNetworkClient->IsConnected()) {
        DebugPrint("Health Check: Network client not connected\r\n");
        bHealthy = FALSE;
    }
    
    // 检查地址管理器
    if (!g_pAddressManager) {
        DebugPrint("Health Check: Address manager not initialized\r\n");
        bHealthy = FALSE;
    }
    
    // 检查原始DLL
    if (!g_hOriginalWinUSB) {
        DebugPrint("Health Check: Original WinUSB DLL not loaded\r\n");
        bHealthy = FALSE;
    }
    
    return bHealthy;
}

// 声明函数原型
BOOL OnProcessAttach(HMODULE hModule);
VOID OnProcessDetach();
VOID InitializeDebugOutput();
VOID CleanupDebugOutput();
VOID DebugPrint(const char* szFormat, ...);
VOID ReportError(const char* szFunction, DWORD dwError);
BOOL LoadConfiguration();
VOID SaveConfiguration();
VOID StartPerformanceMonitoring();
VOID UpdatePerformanceCounters(BOOL bSuccess);
VOID ReportPerformanceStatistics();
BOOL PerformHealthCheck();
