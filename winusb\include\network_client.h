#pragma once
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include "protocol.h"

// 网络客户端类
class NetworkClient {
private:
    SOCKET m_socket;
    CRITICAL_SECTION m_lock;
    BOOL m_bInitialized;
    BOOL m_bConnected;
    DWORD m_dwSequenceNumber;
    
    // 连接配置
    char m_szServerIP[16];
    WORD m_wServerPort;
    DWORD m_dwConnectTimeout;
    DWORD m_dwReadTimeout;
    
    // 统计信息
    DWORD m_dwRequestCount;
    DWORD m_dwErrorCount;
    QWORD m_qwBytesTransferred;
    
public:
    NetworkClient();
    ~NetworkClient();
    
    // 初始化和清理
    BOOL Initialize(const char* szServerIP = "*************", WORD wPort = 28473);
    VOID Cleanup();
    
    // 连接管理
    BOOL Connect();
    VOID Disconnect();
    BOOL IsConnected() const { return m_bConnected; }
    BOOL EnsureConnection();
    
    // 基础通信
    BOOL SendRequest(PNETWORK_DMA_HEADER pRequest, DWORD dwRequestSize);
    BOOL ReceiveResponse(PNETWORK_DMA_HEADER pResponse, DWORD dwMaxResponseSize, PDWORD pdwBytesReceived);
    BOOL SendAndReceive(PNETWORK_DMA_HEADER pRequest, DWORD dwRequestSize, 
                       PVOID pResponseData, DWORD dwMaxResponseSize, PDWORD pdwBytesReceived);
    
    // 高级DMA操作
    BOOL ReadVirtualMemory(DWORD dwProcessId, QWORD qwVirtualAddress, 
                          PBYTE pBuffer, DWORD dwLength, PDWORD pdwBytesRead);
    BOOL WriteVirtualMemory(DWORD dwProcessId, QWORD qwVirtualAddress, 
                           PBYTE pBuffer, DWORD dwLength, PDWORD pdwBytesWritten);
    BOOL ScatterReadVirtualMemory(DWORD dwProcessId, PSCATTER_ENTRY pEntries, 
                                 DWORD dwEntryCount, PBYTE pBuffer, DWORD dwBufferSize);
    
    // CSR操作
    BOOL WriteCSR(WORD wRegAddr, DWORD dwRegValue, BYTE fCSR);
    BOOL ReadCSR(WORD wRegAddr, PDWORD pdwRegValue, BYTE fCSR);
    
    // 工具函数
    DWORD GetNextSequenceNumber();
    BOOL TestConnection();
    VOID GetStatistics(PDWORD pdwRequests, PDWORD pdwErrors, PQWORD pqwBytes);
    
private:
    // 内部辅助函数
    BOOL InitializeWinsock();
    BOOL CreateSocket();
    BOOL SetSocketOptions();
    BOOL ConnectToServer();
    BOOL SendData(PVOID pData, DWORD dwSize);
    BOOL ReceiveData(PVOID pBuffer, DWORD dwSize);
    BOOL ReceiveExactly(PVOID pBuffer, DWORD dwSize);
    VOID CloseSocket();
    VOID UpdateStatistics(BOOL bSuccess, DWORD dwBytesTransferred);
};

// 全局网络客户端实例
extern NetworkClient* g_pNetworkClient;

// 初始化和清理函数
BOOL InitializeNetworkClient();
VOID CleanupNetworkClient();
