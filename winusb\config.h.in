#pragma once

// WinUSB Hijack Configuration
#define WINUSB_HIJACK_VERSION_MAJOR @WINUSB_HIJACK_VERSION_MAJOR@
#define WINUSB_HIJACK_VERSION_MINOR @WINUSB_HIJACK_VERSION_MINOR@
#define WINUSB_HIJACK_VERSION_PATCH @WINUSB_HIJACK_VERSION_PATCH@
#define WINUSB_HIJACK_VERSION "@WINUSB_HIJACK_VERSION@"

// 默认配置
#define DEFAULT_SERVER_IP "*************"
#define DEFAULT_SERVER_PORT 28473
#define DEFAULT_CONNECT_TIMEOUT 5000
#define DEFAULT_READ_TIMEOUT 10000

// 缓存配置
#define ADDRESS_CACHE_SIZE 256
#define PROCESS_HANDLE_CACHE_SIZE 32
#define MEMORY_CACHE_TIMEOUT 5000

// 性能配置
#define MAX_SCATTER_ENTRIES 64
#define MAX_BATCH_REQUESTS 16
#define NETWORK_BUFFER_SIZE 8192

// 调试配置
#ifdef _DEBUG
#define ENABLE_DEBUG_OUTPUT 1
#define ENABLE_PERFORMANCE_MONITORING 1
#else
#define ENABLE_DEBUG_OUTPUT 0
#define ENABLE_PERFORMANCE_MONITORING 0
#endif
