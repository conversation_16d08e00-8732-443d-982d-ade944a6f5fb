基于LeechCore源代码的深入分析，我来详细说明将硬件DMA读取转换为网络请求的最佳实现方案：

## 1. WinUSB劫持分析

### LeechCore对WinUSB的依赖关系

从`leechcore/device_usb3380.c`源代码分析：

````c path=leechcore/device_usb3380.c mode=EXCERPT
typedef struct _DEVICE_DATA {
    BOOL HandlesOpen;
    WINUSB_INTERFACE_HANDLE WinusbHandle;
    HANDLE DeviceHandle;
    WCHAR DevicePath[MAX_PATH];
} DEVICE_DATA, *PDEVICE_DATA;

// 关键的WinUSB调用路径
_Success_(return)
BOOL Device3380_WriteCsr(_In_ PLC_CONTEXT ctxLC, _In_ WORD wRegAddr, _In_ DWORD dwRegValue, _In_ BYTE fCSR)
{
    PDEVICE_DATA ctx = (PDEVICE_DATA)ctxLC->hDevice;
    DWORD cbTransferred;
    PIPE_SEND_CSR_WRITE ps = { .u1 = fCSR | 0x40,.u2 = 0,.u3 = wRegAddr & 0xFF,.u4 = (wRegAddr >> 8) & 0xFF,.dwRegValue = dwRegValue };
    if(wRegAddr & 0x03) { return FALSE; } // must be dword aligned
    return WinUsb_WritePipe(ctx->WinusbHandle, USB_EP_CSROUT, (PUCHAR)&ps, sizeof(ps), &cbTransferred, NULL);
}

VOID Device3380_ReadContigious(_Inout_ PLC_READ_CONTIGIOUS_CONTEXT ctxRC)
{
    PDEVICE_DATA ctx = (PDEVICE_DATA)ctxRC->ctxLC->hDevice;
    // 核心内存读取调用
    if(!WinUsb_ReadPipe(ctx->WinusbHandle, CEP_INFO[ctxRC->iRL].pipe, ctxRC->pb, ctxRC->cb, &ctxRC->cbRead, NULL)) {
        Device3380_ReadContigious_Retry(ctxRC);
    }
}
````

### 为什么选择劫持winusb.dll

````c path=winusb_hijack_analysis.c mode=EDIT
/*
 * 选择劫持winusb.dll的原因分析：
 * 
 * 1. 精确拦截点：LeechCore通过WinUSB与FPGA硬件通信，所有DMA操作都经过WinUSB API
 * 2. 最小侵入性：只需要替换底层通信层，不影响上层逻辑
 * 3. 透明性：对LeechCore和MemProcFS完全透明，无需修改源代码
 * 4. 通用性：适用于所有基于USB的FPGA设备（USB3380、FPGA等）
 */

// 对比不同劫持方案：

// 方案A: 劫持leechcore.dll - 复杂度高
// - 需要劫持大量API函数
// - 影响范围广，容易出错
// - 需要深度理解LeechCore内部结构

// 方案B: 劫持winusb.dll - 推荐方案
// - 只需劫持少数关键函数
// - 拦截点精确，影响范围小
// - 实现相对简单

// 方案C: 传统version.dll劫持 - 不适用
// - LeechCore不依赖version.dll
// - 无法拦截到实际的硬件通信

// WinUSB关键函数分析
typedef struct _WINUSB_FUNCTION_TABLE {
    // 初始化函数
    BOOL (WINAPI *WinUsb_Initialize)(HANDLE DeviceHandle, PWINUSB_INTERFACE_HANDLE InterfaceHandle);
    BOOL (WINAPI *WinUsb_Free)(WINUSB_INTERFACE_HANDLE InterfaceHandle);
    
    // 数据传输函数 - 核心劫持目标
    BOOL (WINAPI *WinUsb_ReadPipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
    BOOL (WINAPI *WinUsb_WritePipe)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred, LPOVERLAPPED Overlapped);
    
    // 配置函数
    BOOL (WINAPI *WinUsb_SetPipePolicy)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, ULONG ValueLength, PVOID Value);
    BOOL (WINAPI *WinUsb_GetPipePolicy)(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, ULONG PolicyType, PULONG ValueLength, PVOID Value);
} WINUSB_FUNCTION_TABLE, *PWINUSB_FUNCTION_TABLE;
````

### WinUSB劫持实现

````c path=winusb_hijack.c mode=EDIT
#include <windows.h>
#include <winusb.h>
#include <winsock2.h>
#include <ws2tcpip.h>

// 原始WinUSB函数指针
static HMODULE g_hOriginalWinUSB = NULL;
static BOOL (WINAPI *g_pfnOriginal_WinUsb_Initialize)(HANDLE, PWINUSB_INTERFACE_HANDLE) = NULL;
static BOOL (WINAPI *g_pfnOriginal_WinUsb_ReadPipe)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED) = NULL;
static BOOL (WINAPI *g_pfnOriginal_WinUsb_WritePipe)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED) = NULL;

// 网络连接管理
static SOCKET g_NetworkSocket = INVALID_SOCKET;
static CRITICAL_SECTION g_NetworkLock;
static BOOL g_bNetworkInitialized = FALSE;

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            DisableThreadLibraryCalls(hModule);
            InitializeCriticalSection(&g_NetworkLock);
            LoadOriginalWinUSB();
            InitializeNetworkConnection();
            break;
            
        case DLL_PROCESS_DETACH:
            CleanupNetworkConnection();
            DeleteCriticalSection(&g_NetworkLock);
            if (g_hOriginalWinUSB) {
                FreeLibrary(g_hOriginalWinUSB);
            }
            break;
    }
    return TRUE;
}

// 加载原始WinUSB.dll
VOID LoadOriginalWinUSB() {
    WCHAR szSystemPath[MAX_PATH];
    WCHAR szWinUSBPath[MAX_PATH];
    
    GetSystemDirectoryW(szSystemPath, MAX_PATH);
    swprintf_s(szWinUSBPath, MAX_PATH, L"%s\\winusb.dll", szSystemPath);
    
    g_hOriginalWinUSB = LoadLibraryW(szWinUSBPath);
    if (g_hOriginalWinUSB) {
        g_pfnOriginal_WinUsb_Initialize = (BOOL(WINAPI*)(HANDLE, PWINUSB_INTERFACE_HANDLE))
            GetProcAddress(g_hOriginalWinUSB, "WinUsb_Initialize");
        g_pfnOriginal_WinUsb_ReadPipe = (BOOL(WINAPI*)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED))
            GetProcAddress(g_hOriginalWinUSB, "WinUsb_ReadPipe");
        g_pfnOriginal_WinUsb_WritePipe = (BOOL(WINAPI*)(WINUSB_INTERFACE_HANDLE, UCHAR, PUCHAR, ULONG, PULONG, LPOVERLAPPED))
            GetProcAddress(g_hOriginalWinUSB, "WinUsb_WritePipe");
    }
}

// 初始化网络连接
BOOL InitializeNetworkConnection() {
    WSADATA wsaData;
    struct sockaddr_in serverAddr;
    
    if (WSAStartup(MAKEWORD(2,2), &wsaData) != 0) {
        return FALSE;
    }
    
    g_NetworkSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (g_NetworkSocket == INVALID_SOCKET) {
        return FALSE;
    }
    
    // 连接到主机端DMA代理服务器
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(28473);
    inet_pton(AF_INET, "*************", &serverAddr.sin_addr); // 主机IP
    
    if (connect(g_NetworkSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        closesocket(g_NetworkSocket);
        g_NetworkSocket = INVALID_SOCKET;
        return FALSE;
    }
    
    g_bNetworkInitialized = TRUE;
    return TRUE;
}
````

## 2. 网络协议设计

### 基于LeechCore数据结构的协议设计

````c path=network_protocol.h mode=EDIT
#pragma pack(push, 1)

// 网络DMA协议头部
typedef struct _NETWORK_DMA_HEADER {
    DWORD dwMagic;              // 0xDEADBEEF
    DWORD dwVersion;            // 协议版本
    DWORD dwCommand;            // 命令类型
    DWORD dwSequence;           // 序列号
    QWORD qwTimestamp;          // 时间戳
    DWORD dwDataLength;         // 数据长度
    DWORD dwChecksum;           // 校验和
} NETWORK_DMA_HEADER, *PNETWORK_DMA_HEADER;

// 基于LC_READ_CONTIGIOUS_CONTEXT的读取请求
typedef struct _NETWORK_DMA_READ_REQUEST {
    NETWORK_DMA_HEADER header;
    QWORD qwPhysicalAddress;    // 物理内存地址
    DWORD dwLength;             // 读取长度
    DWORD dwFlags;              // 标志位
    UCHAR ucPipeID;             // USB管道ID (保持兼容性)
    UCHAR ucReserved[3];        // 对齐
} NETWORK_DMA_READ_REQUEST, *PNETWORK_DMA_READ_REQUEST;

// CSR写入请求 (基于Device3380_WriteCsr)
typedef struct _NETWORK_DMA_CSR_WRITE {
    NETWORK_DMA_HEADER header;
    WORD wRegAddr;              // 寄存器地址
    DWORD dwRegValue;           // 寄存器值
    BYTE fCSR;                  // CSR标志
    UCHAR ucReserved[3];        // 对齐
} NETWORK_DMA_CSR_WRITE, *PNETWORK_DMA_CSR_WRITE;

// 响应数据包
typedef struct _NETWORK_DMA_RESPONSE {
    NETWORK_DMA_HEADER header;
    DWORD dwStatus;             // 状态码 (0=成功)
    DWORD dwErrorCode;          // 错误代码
    BYTE bData[1];              // 可变长度数据
} NETWORK_DMA_RESPONSE, *PNETWORK_DMA_RESPONSE;

#pragma pack(pop)

// 命令类型定义
#define NETWORK_DMA_CMD_READ_MEMORY     0x0001
#define NETWORK_DMA_CMD_WRITE_MEMORY    0x0002
#define NETWORK_DMA_CMD_CSR_READ        0x0003
#define NETWORK_DMA_CMD_CSR_WRITE       0x0004
#define NETWORK_DMA_CMD_PING            0x0005
#define NETWORK_DMA_CMD_DISCONNECT      0x0006

// USB管道ID映射 (保持与原始代码兼容)
#define USB_EP_DMAIN1               0x81
#define USB_EP_DMAIN2               0x82
#define USB_EP_DMAIN3               0x83
#define USB_EP_DMAOUT               0x02
#define USB_EP_CSRIN                0x84
#define USB_EP_CSROUT               0x04
````

### 网络通信实现

````c path=network_communication.c mode=EDIT
// 发送网络DMA请求
BOOL SendNetworkDMARequest(PNETWORK_DMA_HEADER pRequest, DWORD dwRequestSize, 
                          PVOID pResponse, DWORD dwResponseSize, PDWORD pdwBytesReceived) {
    EnterCriticalSection(&g_NetworkLock);
    
    if (!g_bNetworkInitialized || g_NetworkSocket == INVALID_SOCKET) {
        LeaveCriticalSection(&g_NetworkLock);
        return FALSE;
    }
    
    // 计算校验和
    pRequest->dwChecksum = CalculateChecksum((PBYTE)pRequest + sizeof(DWORD), dwRequestSize - sizeof(DWORD));
    
    // 发送请求
    int bytesSent = send(g_NetworkSocket, (char*)pRequest, dwRequestSize, 0);
    if (bytesSent != (int)dwRequestSize) {
        LeaveCriticalSection(&g_NetworkLock);
        return FALSE;
    }
    
    // 接收响应头
    NETWORK_DMA_HEADER responseHeader;
    int bytesReceived = recv(g_NetworkSocket, (char*)&responseHeader, sizeof(responseHeader), MSG_WAITALL);
    if (bytesReceived != sizeof(responseHeader) || responseHeader.dwMagic != 0xBEEFDEAD) {
        LeaveCriticalSection(&g_NetworkLock);
        return FALSE;
    }
    
    // 接收响应数据
    if (responseHeader.dwDataLength > 0 && pResponse && dwResponseSize >= responseHeader.dwDataLength) {
        bytesReceived = recv(g_NetworkSocket, (char*)pResponse, responseHeader.dwDataLength, MSG_WAITALL);
        if (bytesReceived != (int)responseHeader.dwDataLength) {
            LeaveCriticalSection(&g_NetworkLock);
            return FALSE;
        }
        if (pdwBytesReceived) *pdwBytesReceived = bytesReceived;
    }
    
    LeaveCriticalSection(&g_NetworkLock);
    return TRUE;
}

// 校验和计算
DWORD CalculateChecksum(PBYTE pData, DWORD dwLength) {
    DWORD dwChecksum = 0;
    for (DWORD i = 0; i < dwLength; i++) {
        dwChecksum = (dwChecksum << 1) ^ pData[i];
    }
    return dwChecksum;
}
````

## 3. 主机端限制处理

### 虚拟地址转换策略

````c path=address_translation_strategy.c mode=EDIT
// 地址转换策略：在副机端完成虚拟到物理地址转换

// 扩展网络协议支持虚拟地址
typedef struct _NETWORK_DMA_VIRT_READ_REQUEST {
    NETWORK_DMA_HEADER header;
    DWORD dwProcessId;          // 进程ID
    QWORD qwVirtualAddress;     // 虚拟地址
    DWORD dwLength;             // 读取长度
    DWORD dwFlags;              // 标志位
} NETWORK_DMA_VIRT_READ_REQUEST, *PNETWORK_DMA_VIRT_READ_REQUEST;

// 在副机端进行地址转换的实现
BOOL TranslateAndSendMemoryRequest(DWORD dwProcessId, QWORD qwVirtualAddr, 
                                  PBYTE pbBuffer, DWORD dwLength, PDWORD pdwBytesRead) {
    QWORD qwPhysicalAddr;
    NETWORK_DMA_READ_REQUEST request;
    
    // 1. 尝试虚拟地址到物理地址转换
    if (dwProcessId != (DWORD)-1) {
        // 使用MemProcFS的地址转换功能
        if (VmmVirt2Phys_Universal(g_hVMM, dwProcessId, qwVirtualAddr, &qwPhysicalAddr)) {
            // 转换成功，发送物理地址请求
            ZeroMemory(&request, sizeof(request));
            request.header.dwMagic = 0xDEADBEEF;
            request.header.dwCommand = NETWORK_DMA_CMD_READ_MEMORY;
            request.header.dwDataLength = 0;
            request.qwPhysicalAddress = qwPhysicalAddr;
            request.dwLength = dwLength;
            request.dwFlags = 0;
            
            return SendNetworkDMARequest(&request.header, sizeof(request), 
                                       pbBuffer, dwLength, pdwBytesRead);
        } else {
            // 转换失败，发送虚拟地址请求让主机端处理
            NETWORK_DMA_VIRT_READ_REQUEST virtRequest;
            ZeroMemory(&virtRequest, sizeof(virtRequest));
            virtRequest.header.dwMagic = 0xDEADBEEF;
            virtRequest.header.dwCommand = NETWORK_DMA_CMD_READ_VIRTUAL;
            virtRequest.header.dwDataLength = 0;
            virtRequest.dwProcessId = dwProcessId;
            virtRequest.qwVirtualAddress = qwVirtualAddr;
            virtRequest.dwLength = dwLength;
            virtRequest.dwFlags = 0;
            
            return SendNetworkDMARequest(&virtRequest.header, sizeof(virtRequest), 
                                       pbBuffer, dwLength, pdwBytesRead);
        }
    } else {
        // 物理地址直接发送
        ZeroMemory(&request, sizeof(request));
        request.header.dwMagic = 0xDEADBEEF;
        request.header.dwCommand = NETWORK_DMA_CMD_READ_MEMORY;
        request.header.dwDataLength = 0;
        request.qwPhysicalAddress = qwVirtualAddr;
        request.dwLength = dwLength;
        request.dwFlags = 0;
        
        return SendNetworkDMARequest(&request.header, sizeof(request), 
                                   pbBuffer, dwLength, pdwBytesRead);
    }
}

// 集成VMM地址转换功能
static VMM_HANDLE g_hVMM = NULL;

BOOL InitializeVMMForAddressTranslation() {
    LC_CONFIG lcConfig = {0};
    
    // 配置LeechCore使用内存转储或实时内存
    lcConfig.dwVersion = LC_CONFIG_VERSION;
    lcConfig.szDevice = "existing"; // 使用现有的内存访问方式
    
    HANDLE hLC = LcCreate(&lcConfig);
    if (!hLC) return FALSE;
    
    // 初始化VMM
    VMM_CONFIG vmmConfig = {0};
    vmmConfig.dwVersion = VMM_CONFIG_VERSION;
    vmmConfig.hLC = hLC;
    
    g_hVMM = VMMDLL_Initialize(3, (LPSTR[]){"", "-device", "existing"});
    return g_hVMM != NULL;
}
````

## 4. 实现细节

### 关键函数劫持实现

````c path=winusb_function_hijack.c mode=EDIT
// 劫持WinUsb_ReadPipe - 核心内存读取函数
BOOL WINAPI Hooked_WinUsb_ReadPipe(
    WINUSB_INTERFACE_HANDLE InterfaceHandle,
    UCHAR PipeID,
    PUCHAR Buffer,
    ULONG BufferLength,
    PULONG LengthTransferred,
    LPOVERLAPPED Overlapped
) {
    // 检查是否为DMA读取管道
    if (PipeID == USB_EP_DMAIN1 || PipeID == USB_EP_DMAIN2 || PipeID == USB_EP_DMAIN3) {
        return HandleDMAReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 检查是否为CSR读取管道
    if (PipeID == USB_EP_CSRIN) {
        return HandleCSRReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道调用原始函数
    if (g_pfnOriginal_WinUsb_ReadPipe) {
        return g_pfnOriginal_WinUsb_ReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred, Overlapped);
    }
    
    SetLastError(ERROR_INVALID_HANDLE);
    return FALSE;
}

// 处理DMA内存读取
BOOL HandleDMAReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                      PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    NETWORK_DMA_READ_REQUEST request;
    DWORD dwBytesReceived = 0;
    
    // 从上下文中获取物理地址 (需要分析LeechCore的调用上下文)
    QWORD qwPhysicalAddress = GetPhysicalAddressFromContext(InterfaceHandle, PipeID);
    
    // 构造网络请求
    ZeroMemory(&request, sizeof(request));
    request.header.dwMagic = 0xDEADBEEF;
    request.header.dwVersion = 1;
    request.header.dwCommand = NETWORK_DMA_CMD_READ_MEMORY;
    request.header.dwSequence = GetNextSequenceNumber();
    request.header.qwTimestamp = GetTickCount64();
    request.header.dwDataLength = 0;
    request.qwPhysicalAddress = qwPhysicalAddress;
    request.dwLength = BufferLength;
    request.dwFlags = 0;
    request.ucPipeID = PipeID;
    
    // 发送网络请求
    BOOL bResult = SendNetworkDMARequest(&request.header, sizeof(request), 
                                        Buffer, BufferLength, &dwBytesReceived);
    
    if (bResult && LengthTransferred) {
        *LengthTransferred = dwBytesReceived;
    }
    
    return bResult;
}

// 劫持WinUsb_WritePipe - 处理CSR写入和DMA写入
BOOL WINAPI Hooked_WinUsb_WritePipe(
    WINUSB_INTERFACE_HANDLE InterfaceHandle,
    UCHAR PipeID,
    PUCHAR Buffer,
    ULONG BufferLength,
    PULONG LengthTransferred,
    LPOVERLAPPED Overlapped
) {
    // 检查是否为CSR写入管道
    if (PipeID == USB_EP_CSROUT) {
        return HandleCSRWritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 检查是否为DMA写入管道
    if (PipeID == USB_EP_DMAOUT) {
        return HandleDMAWritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道调用原始函数
    if (g_pfnOriginal_WinUsb_WritePipe) {
        return g_pfnOriginal_WinUsb_WritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred, Overlapped);
    }
    
    SetLastError(ERROR_INVALID_HANDLE);
    return FALSE;
}

// 处理CSR寄存器写入
BOOL HandleCSRWritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID, 
                       PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    if (BufferLength != sizeof(PIPE_SEND_CSR_WRITE)) {
        SetLastError(ERROR_INVALID_PARAMETER);
        return FALSE;
    }
    
    PIPE_SEND_CSR_WRITE* pCSR = (PIPE_SEND_CSR_WRITE*)Buffer;
    NETWORK_DMA_CSR_WRITE request;
    
    // 解析CSR写入数据
    WORD wRegAddr = pCSR->u3 | (pCSR->u4 << 8);
    BYTE fCSR = pCSR->u1 & 0x3F; // 移除命令位
    
    // 构造网络请求
    ZeroMemory(&request, sizeof(request));
    request.header.dwMagic = 0xDEADBEEF;
    request.header.dwVersion = 1;
    request.header.dwCommand = NETWORK_DMA_CMD_CSR_WRITE;
    request.header.dwSequence = GetNextSequenceNumber();
    request.header.qwTimestamp = GetTickCount64();
    request.header.dwDataLength = 0;
    request.wRegAddr = wRegAddr;
    request.dwRegValue = pCSR->dwRegValue;
    request.fCSR = fCSR;
    
    // 发送网络请求
    DWORD dwDummy;
    BOOL bResult = SendNetworkDMARequest(&request.header, sizeof(request), NULL, 0, &dwDummy);
    
    if (bResult && LengthTransferred) {
        *LengthTransferred = BufferLength;
    }
    
    return bResult;
}

// 获取物理地址的上下文分析
QWORD GetPhysicalAddressFromContext(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID) {
    // 这里需要分析LeechCore的调用上下文
    // 从Device3380_ReadContigious函数可以看出，物理地址是通过CSR寄存器设置的
    // 我们需要跟踪REG_DMAADDR_X寄存器的写入值
    
    static QWORD s_qwLastDMAAddress[4] = {0}; // 对应4个DMA通道
    
    int channelIndex = -1;
    switch (PipeID) {
        case USB_EP_DMAIN1: channelIndex = 1; break;
        case USB_EP_DMAIN2: channelIndex = 2; break;
        case USB_EP_DMAIN3: channelIndex = 3; break;
        default: channelIndex = 0; break;
    }
    
    if (channelIndex >= 0 && channelIndex < 4) {
        return s_qwLastDMAAddress[channelIndex];
    }
    
    return 0;
}

// 序列号管理
static DWORD g_dwSequenceNumber = 0;

DWORD GetNextSequenceNumber() {
    return InterlockedIncrement(&g_dwSequenceNumber);
}
````

### 错误处理和性能优化

````c path=network_optimization.c mode=EDIT
// 网络连接重连机制
BOOL EnsureNetworkConnection() {
    if (g_NetworkSocket != INVALID_SOCKET) {
        // 测试连接是否有效
        NETWORK_DMA_HEADER pingRequest = {0};
        pingRequest.dwMagic = 0xDEADBEEF;
        pingRequest.dwCommand = NETWORK_DMA_CMD_PING;
        pingRequest.dwDataLength = 0;
        
        int result = send(g_NetworkSocket, (char*)&pingRequest, sizeof(pingRequest), 0);
        if (result == sizeof(pingRequest)) {
            return TRUE; // 连接正常
        }
        
        // 连接异常，关闭并重连
        closesocket(g_NetworkSocket);
        g_NetworkSocket = INVALID_SOCKET;
    }
    
    // 重新建立连接
    return InitializeNetworkConnection();
}

// 性能优化：批量请求处理
typedef struct _BATCH_REQUEST_CONTEXT {
    DWORD dwRequestCount;
    NETWORK_DMA_READ_REQUEST requests[16]; // 最多批量处理16个请求
    PBYTE pBuffers[16];
    DWORD dwLengths[16];
} BATCH_REQUEST_CONTEXT, *PBATCH_REQUEST_CONTEXT;

static BATCH_REQUEST_CONTEXT g_BatchContext = {0};
static CRITICAL_SECTION g_BatchLock;

// 批量发送DMA请求
BOOL ProcessBatchRequests() {
    if (g_BatchContext.dwRequestCount == 0) {
        return TRUE;
    }
    
    EnterCriticalSection(&g_BatchLock);
    
    // 发送批量请求头
    NETWORK_DMA_HEADER batchHeader = {0};
    batchHeader.dwMagic = 0xDEADBEEF;
    batchHeader.dwCommand = NETWORK_DMA_CMD_BATCH_READ;
    batchHeader.dwDataLength = g_BatchContext.dwRequestCount * sizeof(NETWORK_DMA_READ_REQUEST);
    
    // 发送请求
    BOOL bResult = TRUE;
    if (send(g_NetworkSocket, (char*)&batchHeader, sizeof(batchHeader), 0) == sizeof(batchHeader)) {
        if (send(g_NetworkSocket, (char*)g_BatchContext.requests, batchHeader.dwDataLength, 0) == (int)batchHeader.dwDataLength) {
            // 接收批量响应
            for (DWORD i = 0; i < g_BatchContext.dwRequestCount; i++) {
                DWORD dwBytesReceived;
                int result = recv(g_NetworkSocket, (char*)g_BatchContext.pBuffers[i], g_BatchContext.dwLengths[i], MSG_WAITALL);
                if (result != (int)g_BatchContext.dwLengths[i]) {
                    bResult = FALSE;
                    break;
                }
            }
        } else {
            bResult = FALSE;
        }
    } else {
        bResult = FALSE;
    }
    
    // 清空批量上下文
    g_BatchContext.dwRequestCount = 0;
    
    LeaveCriticalSection(&g_BatchLock);
    return bResult;
}

// 缓存机制：缓存最近读取的内存页
#define MEMORY_CACHE_SIZE 64
typedef struct _MEMORY_CACHE_ENTRY {
    QWORD qwPhysicalAddress;
    DWORD dwLength;
    BYTE bData[4096]; // 4KB页面
    DWORD dwTimestamp;
    BOOL bValid;
} MEMORY_CACHE_ENTRY, *PMEMORY_CACHE_ENTRY;

static MEMORY_CACHE_ENTRY g_MemoryCache[MEMORY_CACHE_SIZE];
static DWORD g_dwCacheIndex = 0;

// 从缓存中查找内存数据
BOOL GetFromMemoryCache(QWORD qwPhysicalAddress, PBYTE pBuffer, DWORD dwLength) {
    QWORD qwPageAddress = qwPhysicalAddress & ~0xFFF;
    DWORD dwOffset = (DWORD)(qwPhysicalAddress & 0xFFF);
    
    if (dwOffset + dwLength > 4096) {
        return FALSE; // 跨页访问，不使用缓存
    }
    
    for (DWORD i = 0; i < MEMORY_CACHE_SIZE; i++) {
        if (g_MemoryCache[i].bValid && 
            g_MemoryCache[i].qwPhysicalAddress == qwPageAddress &&
            GetTickCount() - g_MemoryCache[i].dwTimestamp < 5000) { // 5秒缓存有效期
            
            memcpy(pBuffer, g_MemoryCache[i].bData + dwOffset, dwLength);
            return TRUE;
        }
    }
    
    return FALSE;
}

// 将数据添加到缓存
VOID AddToMemoryCache(QWORD qwPhysicalAddress, PBYTE pData, DWORD dwLength) {
    if (dwLength != 4096) return; // 只缓存完整页面
    
    DWORD index = g_dwCacheIndex % MEMORY_CACHE_SIZE;
    g_MemoryCache[index].qwPhysicalAddress = qwPhysicalAddress & ~0xFFF;
    g_MemoryCache[index].dwLength = dwLength;
    memcpy(g_MemoryCache[index].bData, pData, dwLength);
    g_MemoryCache[index].dwTimestamp = GetTickCount();
    g_MemoryCache[index].bValid = TRUE;
    
    g_dwCacheIndex++;
}
````

## 总结

这个实现方案的优势：

1. **最小侵入性**：只需要替换winusb.dll，对LeechCore和MemProcFS完全透明
2. **精确拦截**：在最底层的硬件通信接口进行拦截，确保所有DMA操作都被捕获
3. **协议兼容性**：保持与原有LeechCore API的完全兼容
4. **性能优化**：通过批量处理、缓存机制等提高网络传输效率
5. **错误恢复**：具备网络重连、错误处理等健壮性机制

这种方案可以将任何基于LeechCore的应用（如MemProcFS、PCILeech）无缝转换为网络模式，实现远程内存访问功能。
