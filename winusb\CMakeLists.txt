cmake_minimum_required(VERSION 3.16)
project(WinUSB_Hijack)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 编译器特定设置
if(MSVC)
    # 使用多线程编译
    add_compile_options(/MP)
    
    # 设置警告级别
    add_compile_options(/W3)
    
    # 禁用特定警告
    add_compile_options(/wd4996)  # 禁用不安全函数警告
    
    # 设置字符集
    add_compile_definitions(UNICODE _UNICODE)
    
    # 设置Windows版本
    add_compile_definitions(_WIN32_WINNT=0x0601)  # Windows 7+
    
    # 优化设置
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(/O2 /Ob2 /DNDEBUG)
        add_link_options(/OPT:REF /OPT:ICF)
    endif()
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# 源文件
set(SOURCES
    src/dllmain.cpp
    src/winusb_hooks.cpp
    src/network_client.cpp
    src/address_manager.cpp
    src/protocol.cpp
)

# 头文件
set(HEADERS
    include/winusb_hooks.h
    include/network_client.h
    include/address_manager.h
    include/protocol.h
)

# 创建共享库 (DLL)
add_library(winusb SHARED ${SOURCES} ${HEADERS})

# 设置DLL导出定义文件
set_target_properties(winusb PROPERTIES
    LINK_FLAGS "/DEF:${CMAKE_CURRENT_SOURCE_DIR}/exports/winusb.def"
)

# 链接库
target_link_libraries(winusb
    ws2_32      # Winsock2
    psapi       # Process API
    kernel32    # Windows Kernel
    user32      # Windows User
    advapi32    # Advanced Windows API
)

# 设置输出名称
set_target_properties(winusb PROPERTIES
    OUTPUT_NAME "winusb"
    PREFIX ""
    SUFFIX ".dll"
)

# 调试配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(winusb PRIVATE _DEBUG DEBUG)
    set_target_properties(winusb PROPERTIES
        DEBUG_POSTFIX "_d"
    )
endif()

# 安装规则
install(TARGETS winusb
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 安装头文件
install(DIRECTORY include/
    DESTINATION include/winusb_hijack
    FILES_MATCHING PATTERN "*.h"
)

# 创建配置文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/config.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/config.h"
    @ONLY
)

# 添加配置文件到包含路径
target_include_directories(winusb PRIVATE ${CMAKE_CURRENT_BINARY_DIR})

# 自定义目标：清理日志文件
add_custom_target(clean_logs
    COMMAND ${CMAKE_COMMAND} -E remove -f "${CMAKE_BINARY_DIR}/winusb_hijack.log"
    COMMENT "Cleaning log files"
)

# 自定义目标：复制到系统目录 (需要管理员权限)
add_custom_target(install_system
    COMMAND ${CMAKE_COMMAND} -E copy_if_different 
        "$<TARGET_FILE:winusb>" 
        "C:/Windows/System32/winusb_backup.dll"
    DEPENDS winusb
    COMMENT "Installing to system directory (requires admin rights)"
)

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Output directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")

# 版本信息
set(WINUSB_HIJACK_VERSION_MAJOR 1)
set(WINUSB_HIJACK_VERSION_MINOR 0)
set(WINUSB_HIJACK_VERSION_PATCH 0)
set(WINUSB_HIJACK_VERSION "${WINUSB_HIJACK_VERSION_MAJOR}.${WINUSB_HIJACK_VERSION_MINOR}.${WINUSB_HIJACK_VERSION_PATCH}")

target_compile_definitions(winusb PRIVATE
    WINUSB_HIJACK_VERSION_MAJOR=${WINUSB_HIJACK_VERSION_MAJOR}
    WINUSB_HIJACK_VERSION_MINOR=${WINUSB_HIJACK_VERSION_MINOR}
    WINUSB_HIJACK_VERSION_PATCH=${WINUSB_HIJACK_VERSION_PATCH}
    WINUSB_HIJACK_VERSION="${WINUSB_HIJACK_VERSION}"
)

message(STATUS "WinUSB Hijack version: ${WINUSB_HIJACK_VERSION}")
