#include "../include/protocol.h"

// 计算校验和
DWORD CalculateChecksum(PBYTE pData, DWORD dwLength) {
    DWORD dwChecksum = 0;
    for (DWORD i = 0; i < dwLength; i++) {
        dwChecksum = (dwChecksum << 1) ^ pData[i];
        // 防止溢出
        if (dwChecksum & 0x80000000) {
            dwChecksum = (dwChecksum << 1) | 1;
        }
    }
    return dwChecksum;
}

// 验证协议头
BOOL ValidateHeader(PNETWORK_DMA_HEADER pHeader, DWORD dwExpectedCommand) {
    if (!pHeader) {
        return FALSE;
    }
    
    // 检查魔数
    if (pHeader->dwMagic != NETWORK_DMA_MAGIC && 
        pHeader->dwMagic != NETWORK_DMA_RESPONSE_MAGIC) {
        return FALSE;
    }
    
    // 检查版本
    if (pHeader->dwVersion != NETWORK_DMA_VERSION) {
        return FALSE;
    }
    
    // 检查命令类型 (如果指定)
    if (dwExpectedCommand != 0 && pHeader->dwCommand != dwExpectedCommand) {
        return FALSE;
    }
    
    // 检查数据长度合理性
    if (pHeader->dwDataLength > 0x10000000) { // 256MB限制
        return FALSE;
    }
    
    return TRUE;
}

// 初始化请求头
VOID InitializeRequestHeader(PNETWORK_DMA_HEADER pHeader, DWORD dwCommand, 
                           DWORD dwSequence, DWORD dwDataLength) {
    if (!pHeader) return;
    
    ZeroMemory(pHeader, sizeof(NETWORK_DMA_HEADER));
    pHeader->dwMagic = NETWORK_DMA_MAGIC;
    pHeader->dwVersion = NETWORK_DMA_VERSION;
    pHeader->dwCommand = dwCommand;
    pHeader->dwSequence = dwSequence;
    pHeader->qwTimestamp = GetTickCount64();
    pHeader->dwDataLength = dwDataLength;
    pHeader->dwChecksum = 0; // 将在发送前计算
}

// 初始化响应头
VOID InitializeResponseHeader(PNETWORK_DMA_HEADER pHeader, DWORD dwCommand, 
                            DWORD dwSequence, DWORD dwDataLength) {
    if (!pHeader) return;
    
    ZeroMemory(pHeader, sizeof(NETWORK_DMA_HEADER));
    pHeader->dwMagic = NETWORK_DMA_RESPONSE_MAGIC;
    pHeader->dwVersion = NETWORK_DMA_VERSION;
    pHeader->dwCommand = dwCommand;
    pHeader->dwSequence = dwSequence;
    pHeader->qwTimestamp = GetTickCount64();
    pHeader->dwDataLength = dwDataLength;
    pHeader->dwChecksum = 0; // 将在发送前计算
}

// 验证校验和
BOOL ValidateChecksum(PNETWORK_DMA_HEADER pHeader, DWORD dwTotalSize) {
    if (!pHeader || dwTotalSize < sizeof(NETWORK_DMA_HEADER)) {
        return FALSE;
    }
    
    DWORD dwStoredChecksum = pHeader->dwChecksum;
    pHeader->dwChecksum = 0;
    
    DWORD dwCalculatedChecksum = CalculateChecksum((PBYTE)pHeader, dwTotalSize);
    pHeader->dwChecksum = dwStoredChecksum;
    
    return dwStoredChecksum == dwCalculatedChecksum;
}

// 设置校验和
VOID SetChecksum(PNETWORK_DMA_HEADER pHeader, DWORD dwTotalSize) {
    if (!pHeader || dwTotalSize < sizeof(NETWORK_DMA_HEADER)) {
        return;
    }
    
    pHeader->dwChecksum = 0;
    pHeader->dwChecksum = CalculateChecksum((PBYTE)pHeader, dwTotalSize);
}

// 获取命令名称 (用于调试)
const char* GetCommandName(DWORD dwCommand) {
    switch (dwCommand) {
        case NETWORK_DMA_CMD_READ_MEMORY:   return "READ_MEMORY";
        case NETWORK_DMA_CMD_WRITE_MEMORY:  return "WRITE_MEMORY";
        case NETWORK_DMA_CMD_CSR_READ:      return "CSR_READ";
        case NETWORK_DMA_CMD_CSR_WRITE:     return "CSR_WRITE";
        case NETWORK_DMA_CMD_PING:          return "PING";
        case NETWORK_DMA_CMD_DISCONNECT:    return "DISCONNECT";
        case NETWORK_DMA_CMD_READ_VIRTUAL:  return "READ_VIRTUAL";
        case NETWORK_DMA_CMD_SCATTER_READ:  return "SCATTER_READ";
        case NETWORK_DMA_CMD_WRITE_VIRTUAL: return "WRITE_VIRTUAL";
        default:                            return "UNKNOWN";
    }
}

// 获取状态名称 (用于调试)
const char* GetStatusName(DWORD dwStatus) {
    switch (dwStatus) {
        case NETWORK_DMA_STATUS_SUCCESS:            return "SUCCESS";
        case NETWORK_DMA_STATUS_INVALID_REQUEST:    return "INVALID_REQUEST";
        case NETWORK_DMA_STATUS_ACCESS_DENIED:      return "ACCESS_DENIED";
        case NETWORK_DMA_STATUS_PROCESS_NOT_FOUND:  return "PROCESS_NOT_FOUND";
        case NETWORK_DMA_STATUS_MEMORY_ERROR:       return "MEMORY_ERROR";
        case NETWORK_DMA_STATUS_NETWORK_ERROR:      return "NETWORK_ERROR";
        case NETWORK_DMA_STATUS_TIMEOUT:            return "TIMEOUT";
        default:                                    return "UNKNOWN";
    }
}

// 创建虚拟内存读取请求
BOOL CreateVirtualReadRequest(PNETWORK_DMA_VIRT_READ_REQUEST pRequest, 
                             DWORD dwProcessId, QWORD qwVirtualAddress, 
                             DWORD dwLength, UCHAR ucPipeID, DWORD dwSequence) {
    if (!pRequest) return FALSE;
    
    ZeroMemory(pRequest, sizeof(NETWORK_DMA_VIRT_READ_REQUEST));
    InitializeRequestHeader(&pRequest->header, NETWORK_DMA_CMD_READ_VIRTUAL, 
                           dwSequence, 0);
    
    pRequest->dwProcessId = dwProcessId;
    pRequest->qwVirtualAddress = qwVirtualAddress;
    pRequest->dwLength = dwLength;
    pRequest->dwFlags = 0;
    pRequest->ucPipeID = ucPipeID;
    
    SetChecksum(&pRequest->header, sizeof(NETWORK_DMA_VIRT_READ_REQUEST));
    return TRUE;
}

// 创建scatter读取请求
BOOL CreateScatterReadRequest(PNETWORK_DMA_SCATTER_REQUEST pRequest, 
                             DWORD dwProcessId, DWORD dwEntryCount, 
                             DWORD dwTotalBufferSize, UCHAR ucPipeID, DWORD dwSequence) {
    if (!pRequest) return FALSE;
    
    ZeroMemory(pRequest, sizeof(NETWORK_DMA_SCATTER_REQUEST));
    InitializeRequestHeader(&pRequest->header, NETWORK_DMA_CMD_SCATTER_READ, 
                           dwSequence, dwEntryCount * sizeof(SCATTER_ENTRY));
    
    pRequest->dwProcessId = dwProcessId;
    pRequest->dwEntryCount = dwEntryCount;
    pRequest->dwTotalBufferSize = dwTotalBufferSize;
    pRequest->ucPipeID = ucPipeID;
    
    // 注意：校验和将在添加scatter条目后设置
    return TRUE;
}

// 创建CSR写入请求
BOOL CreateCSRWriteRequest(PNETWORK_DMA_CSR_WRITE pRequest, 
                          WORD wRegAddr, DWORD dwRegValue, BYTE fCSR, DWORD dwSequence) {
    if (!pRequest) return FALSE;
    
    ZeroMemory(pRequest, sizeof(NETWORK_DMA_CSR_WRITE));
    InitializeRequestHeader(&pRequest->header, NETWORK_DMA_CMD_CSR_WRITE, 
                           dwSequence, 0);
    
    pRequest->wRegAddr = wRegAddr;
    pRequest->dwRegValue = dwRegValue;
    pRequest->fCSR = fCSR;
    
    SetChecksum(&pRequest->header, sizeof(NETWORK_DMA_CSR_WRITE));
    return TRUE;
}
