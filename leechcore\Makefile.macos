CC=clang
CFLAGS  += -I. -D MACOS -D _GNU_SOURCE -fPIC -fvisibility=hidden -pthread
CFLAGS += -fPIC -fstack-protector-strong -D_FORTIFY_SOURCE=2 -O1
CFLAGS += -Wall -Wno-multichar -Wno-unused-result -Wno-unused-variable -Wno-unused-value
CFLAGS += -Wno-pointer-to-int-cast -Wno-int-to-pointer-cast
CFLAGS += -mmacosx-version-min=11.0
# DEBUG FLAGS BELOW
#CFLAGS += -O0
#CFLAGS += -fsanitize=address
# DEBUG FLAGS ABOVE
LDFLAGS += -g -dynamiclib -mmacosx-version-min=11.0

DEPS = leechcore.h
OBJ = oscompatibility.o leechcore.o util.o memmap.o device_file.o device_fpga.o device_hibr.o device_pmem.o device_tmd.o device_usb3380.o device_vmm.o device_vmware.o leechrpcclient.o ob/ob_core.o ob/ob_map.o ob/ob_set.o ob/ob_bytequeue.o

# ARCH SPECIFIC FLAGS:
CFLAGS_X86_64  = $(CFLAGS) -arch x86_64
CFLAGS_ARM64   = $(CFLAGS) -arch arm64
LDFLAGS_X86_64 = $(LDFLAGS) -arch x86_64
LDFLAGS_ARM64  = $(LDFLAGS) -arch arm64
OBJ_X86_64 = $(OBJ:.o=.o.x86_64)
OBJ_ARM64  = $(OBJ:.o=.o.arm64)

all: leechcore.dylib

%.o.x86_64: %.c $(DEPS)
	$(CC) $(CFLAGS_X86_64) -c -o $@ $<

%.o.arm64: %.c $(DEPS)
	$(CC) $(CFLAGS_ARM64) -c -o $@ $<

leechcore_x86_64.dylib: $(OBJ_X86_64)
	$(CC) $(LDFLAGS_X86_64) -o $@ $^

leechcore_arm64.dylib: $(OBJ_ARM64)
	$(CC) $(LDFLAGS_ARM64) -o $@ $^

leechcore.dylib: leechcore_x86_64.dylib leechcore_arm64.dylib
	lipo -create -output leechcore.dylib leechcore_x86_64.dylib leechcore_arm64.dylib
	install_name_tool -id @rpath/leechcore.dylib leechcore.dylib
	mv leechcore.dylib ../files/
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
	true

clean:
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
