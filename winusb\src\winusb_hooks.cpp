#include "../include/winusb_hooks.h"
#include "../include/network_client.h"
#include "../include/address_manager.h"
#include "../include/protocol.h"
#include <stdio.h>

// 原始函数指针
PFN_WinUsb_Initialize g_pfnOriginal_WinUsb_Initialize = nullptr;
PFN_WinUsb_Free g_pfnOriginal_WinUsb_Free = nullptr;
PFN_WinUsb_ReadPipe g_pfnOriginal_WinUsb_ReadPipe = nullptr;
PFN_WinUsb_WritePipe g_pfnOriginal_WinUsb_WritePipe = nullptr;
PFN_WinUsb_SetPipePolicy g_pfnOriginal_WinUsb_SetPipePolicy = nullptr;
PFN_WinUsb_GetPipePolicy g_pfnOriginal_WinUsb_GetPipePolicy = nullptr;
PFN_WinUsb_ControlTransfer g_pfnOriginal_WinUsb_ControlTransfer = nullptr;
PFN_WinUsb_ResetPipe g_pfnOriginal_WinUsb_ResetPipe = nullptr;
PFN_WinUsb_AbortPipe g_pfnOriginal_WinUsb_AbortPipe = nullptr;
PFN_WinUsb_FlushPipe g_pfnOriginal_WinUsb_FlushPipe = nullptr;

// 原始WinUSB模块句柄
HMODULE g_hOriginalWinUSB = nullptr;

// DMA地址跟踪
DMA_ADDRESS_TRACKER g_dmaTracker = {0};
CRITICAL_SECTION g_dmaTrackerLock;

// 网络模式控制
static BOOL g_bNetworkModeEnabled = TRUE;
static CRITICAL_SECTION g_networkModeLock;

BOOL LoadOriginalWinUSB() {
    WCHAR szSystemPath[MAX_PATH];
    WCHAR szWinUSBPath[MAX_PATH];
    
    // 获取系统目录
    if (GetSystemDirectoryW(szSystemPath, MAX_PATH) == 0) {
        return FALSE;
    }
    
    // 构造原始winusb.dll路径
    swprintf_s(szWinUSBPath, MAX_PATH, L"%s\\winusb.dll", szSystemPath);
    
    // 加载原始DLL
    g_hOriginalWinUSB = LoadLibraryW(szWinUSBPath);
    if (!g_hOriginalWinUSB) {
        return FALSE;
    }
    
    // 获取函数地址
    g_pfnOriginal_WinUsb_Initialize = (PFN_WinUsb_Initialize)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_Initialize");
    g_pfnOriginal_WinUsb_Free = (PFN_WinUsb_Free)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_Free");
    g_pfnOriginal_WinUsb_ReadPipe = (PFN_WinUsb_ReadPipe)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_ReadPipe");
    g_pfnOriginal_WinUsb_WritePipe = (PFN_WinUsb_WritePipe)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_WritePipe");
    g_pfnOriginal_WinUsb_SetPipePolicy = (PFN_WinUsb_SetPipePolicy)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_SetPipePolicy");
    g_pfnOriginal_WinUsb_GetPipePolicy = (PFN_WinUsb_GetPipePolicy)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_GetPipePolicy");
    g_pfnOriginal_WinUsb_ControlTransfer = (PFN_WinUsb_ControlTransfer)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_ControlTransfer");
    g_pfnOriginal_WinUsb_ResetPipe = (PFN_WinUsb_ResetPipe)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_ResetPipe");
    g_pfnOriginal_WinUsb_AbortPipe = (PFN_WinUsb_AbortPipe)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_AbortPipe");
    g_pfnOriginal_WinUsb_FlushPipe = (PFN_WinUsb_FlushPipe)
        GetProcAddress(g_hOriginalWinUSB, "WinUsb_FlushPipe");
    
    // 检查关键函数是否加载成功
    if (!g_pfnOriginal_WinUsb_Initialize || !g_pfnOriginal_WinUsb_ReadPipe || 
        !g_pfnOriginal_WinUsb_WritePipe) {
        UnloadOriginalWinUSB();
        return FALSE;
    }
    
    return TRUE;
}

VOID UnloadOriginalWinUSB() {
    if (g_hOriginalWinUSB) {
        FreeLibrary(g_hOriginalWinUSB);
        g_hOriginalWinUSB = nullptr;
    }
    
    // 清空函数指针
    g_pfnOriginal_WinUsb_Initialize = nullptr;
    g_pfnOriginal_WinUsb_Free = nullptr;
    g_pfnOriginal_WinUsb_ReadPipe = nullptr;
    g_pfnOriginal_WinUsb_WritePipe = nullptr;
    g_pfnOriginal_WinUsb_SetPipePolicy = nullptr;
    g_pfnOriginal_WinUsb_GetPipePolicy = nullptr;
    g_pfnOriginal_WinUsb_ControlTransfer = nullptr;
    g_pfnOriginal_WinUsb_ResetPipe = nullptr;
    g_pfnOriginal_WinUsb_AbortPipe = nullptr;
    g_pfnOriginal_WinUsb_FlushPipe = nullptr;
}

BOOL InitializeHooks() {
    InitializeCriticalSection(&g_dmaTrackerLock);
    InitializeCriticalSection(&g_networkModeLock);
    
    // 初始化DMA跟踪器
    ZeroMemory(&g_dmaTracker, sizeof(g_dmaTracker));
    
    return TRUE;
}

VOID CleanupHooks() {
    DeleteCriticalSection(&g_dmaTrackerLock);
    DeleteCriticalSection(&g_networkModeLock);
}

// 劫持函数实现
extern "C" BOOL WINAPI WinUsb_Initialize(HANDLE DeviceHandle, 
                                        PWINUSB_INTERFACE_HANDLE InterfaceHandle) {
    if (!g_pfnOriginal_WinUsb_Initialize) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    
    return g_pfnOriginal_WinUsb_Initialize(DeviceHandle, InterfaceHandle);
}

extern "C" BOOL WINAPI WinUsb_Free(WINUSB_INTERFACE_HANDLE InterfaceHandle) {
    if (!g_pfnOriginal_WinUsb_Free) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    
    return g_pfnOriginal_WinUsb_Free(InterfaceHandle);
}

extern "C" BOOL WINAPI WinUsb_ReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle,
                                      UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength,
                                      PULONG LengthTransferred, LPOVERLAPPED Overlapped) {
    // 检查是否启用网络模式
    if (!IsNetworkModeEnabled()) {
        if (!g_pfnOriginal_WinUsb_ReadPipe) {
            SetLastError(ERROR_PROC_NOT_FOUND);
            return FALSE;
        }
        return g_pfnOriginal_WinUsb_ReadPipe(InterfaceHandle, PipeID, Buffer, 
                                           BufferLength, LengthTransferred, Overlapped);
    }
    
    // 检查是否为DMA读取管道
    if (PipeID == USB_EP_DMAIN1 || PipeID == USB_EP_DMAIN2 || PipeID == USB_EP_DMAIN3) {
        return HandleDMAReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 检查是否为CSR读取管道
    if (PipeID == USB_EP_CSRIN) {
        return HandleCSRReadPipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道调用原始函数
    if (!g_pfnOriginal_WinUsb_ReadPipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    
    return g_pfnOriginal_WinUsb_ReadPipe(InterfaceHandle, PipeID, Buffer, 
                                       BufferLength, LengthTransferred, Overlapped);
}

extern "C" BOOL WINAPI WinUsb_WritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle,
                                       UCHAR PipeID, PUCHAR Buffer, ULONG BufferLength,
                                       PULONG LengthTransferred, LPOVERLAPPED Overlapped) {
    // 检查是否启用网络模式
    if (!IsNetworkModeEnabled()) {
        if (!g_pfnOriginal_WinUsb_WritePipe) {
            SetLastError(ERROR_PROC_NOT_FOUND);
            return FALSE;
        }
        return g_pfnOriginal_WinUsb_WritePipe(InterfaceHandle, PipeID, Buffer, 
                                            BufferLength, LengthTransferred, Overlapped);
    }
    
    // 检查是否为CSR写入管道
    if (PipeID == USB_EP_CSROUT) {
        return HandleCSRWritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 检查是否为DMA写入管道
    if (PipeID == USB_EP_DMAOUT) {
        return HandleDMAWritePipe(InterfaceHandle, PipeID, Buffer, BufferLength, LengthTransferred);
    }
    
    // 其他管道调用原始函数
    if (!g_pfnOriginal_WinUsb_WritePipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    
    return g_pfnOriginal_WinUsb_WritePipe(InterfaceHandle, PipeID, Buffer, 
                                        BufferLength, LengthTransferred, Overlapped);
}

// 其他WinUSB函数的直接转发
extern "C" BOOL WINAPI WinUsb_SetPipePolicy(WINUSB_INTERFACE_HANDLE InterfaceHandle,
                                           UCHAR PipeID, ULONG PolicyType, 
                                           ULONG ValueLength, PVOID Value) {
    if (!g_pfnOriginal_WinUsb_SetPipePolicy) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_SetPipePolicy(InterfaceHandle, PipeID, PolicyType, ValueLength, Value);
}

extern "C" BOOL WINAPI WinUsb_GetPipePolicy(WINUSB_INTERFACE_HANDLE InterfaceHandle,
                                           UCHAR PipeID, ULONG PolicyType, 
                                           PULONG ValueLength, PVOID Value) {
    if (!g_pfnOriginal_WinUsb_GetPipePolicy) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_GetPipePolicy(InterfaceHandle, PipeID, PolicyType, ValueLength, Value);
}

extern "C" BOOL WINAPI WinUsb_ControlTransfer(WINUSB_INTERFACE_HANDLE InterfaceHandle,
                                             WINUSB_SETUP_PACKET SetupPacket, PUCHAR Buffer,
                                             ULONG BufferLength, PULONG LengthTransferred,
                                             LPOVERLAPPED Overlapped) {
    if (!g_pfnOriginal_WinUsb_ControlTransfer) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_ControlTransfer(InterfaceHandle, SetupPacket, Buffer, 
                                              BufferLength, LengthTransferred, Overlapped);
}

extern "C" BOOL WINAPI WinUsb_ResetPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID) {
    if (!g_pfnOriginal_WinUsb_ResetPipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_ResetPipe(InterfaceHandle, PipeID);
}

extern "C" BOOL WINAPI WinUsb_AbortPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID) {
    if (!g_pfnOriginal_WinUsb_AbortPipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_AbortPipe(InterfaceHandle, PipeID);
}

extern "C" BOOL WINAPI WinUsb_FlushPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID) {
    if (!g_pfnOriginal_WinUsb_FlushPipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }
    return g_pfnOriginal_WinUsb_FlushPipe(InterfaceHandle, PipeID);
}

// DMA处理函数实现
BOOL HandleDMAReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID,
                      PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    if (!Buffer || BufferLength == 0 || !g_pNetworkClient || !g_pAddressManager) {
        SetLastError(ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    // 获取DMA上下文
    DMA_CONTEXT dmaContext;
    if (!g_pAddressManager->GetDMAContext(&dmaContext)) {
        SetLastError(ERROR_INVALID_HANDLE);
        return FALSE;
    }

    // 获取当前进程ID (LeechCore通常读取当前进程或指定进程)
    DWORD dwProcessId = dmaContext.dwProcessId;
    if (dwProcessId == 0) {
        dwProcessId = GetCurrentProcessId();
    }

    // 获取DMA地址
    QWORD qwVirtualAddress = GetDMAAddress(PipeID);
    if (qwVirtualAddress == 0) {
        qwVirtualAddress = dmaContext.qwBaseAddress;
    }

    // 执行虚拟内存读取
    DWORD dwBytesRead = 0;
    BOOL bResult = g_pNetworkClient->ReadVirtualMemory(dwProcessId, qwVirtualAddress,
                                                      Buffer, BufferLength, &dwBytesRead);

    if (bResult && LengthTransferred) {
        *LengthTransferred = dwBytesRead;
    }

    return bResult;
}

BOOL HandleDMAWritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID,
                       PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    if (!Buffer || BufferLength == 0 || !g_pNetworkClient || !g_pAddressManager) {
        SetLastError(ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    // 获取DMA上下文
    DMA_CONTEXT dmaContext;
    if (!g_pAddressManager->GetDMAContext(&dmaContext)) {
        SetLastError(ERROR_INVALID_HANDLE);
        return FALSE;
    }

    // 获取当前进程ID
    DWORD dwProcessId = dmaContext.dwProcessId;
    if (dwProcessId == 0) {
        dwProcessId = GetCurrentProcessId();
    }

    // 获取DMA地址
    QWORD qwVirtualAddress = GetDMAAddress(PipeID);
    if (qwVirtualAddress == 0) {
        qwVirtualAddress = dmaContext.qwBaseAddress;
    }

    // 执行虚拟内存写入
    DWORD dwBytesWritten = 0;
    BOOL bResult = g_pNetworkClient->WriteVirtualMemory(dwProcessId, qwVirtualAddress,
                                                       Buffer, BufferLength, &dwBytesWritten);

    if (bResult && LengthTransferred) {
        *LengthTransferred = dwBytesWritten;
    }

    return bResult;
}

BOOL HandleCSRReadPipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID,
                      PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    // CSR读取暂时使用原始函数处理
    // 在实际实现中，可能需要模拟CSR寄存器的读取
    if (!g_pfnOriginal_WinUsb_ReadPipe) {
        SetLastError(ERROR_PROC_NOT_FOUND);
        return FALSE;
    }

    return g_pfnOriginal_WinUsb_ReadPipe(InterfaceHandle, PipeID, Buffer,
                                       BufferLength, LengthTransferred, nullptr);
}

BOOL HandleCSRWritePipe(WINUSB_INTERFACE_HANDLE InterfaceHandle, UCHAR PipeID,
                       PUCHAR Buffer, ULONG BufferLength, PULONG LengthTransferred) {
    if (!Buffer || BufferLength != sizeof(PIPE_SEND_CSR_WRITE) || !g_pNetworkClient) {
        SetLastError(ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    PIPE_SEND_CSR_WRITE* pCSR = (PIPE_SEND_CSR_WRITE*)Buffer;

    // 解析CSR写入数据
    WORD wRegAddr = pCSR->u3 | (pCSR->u4 << 8);
    BYTE fCSR = pCSR->u1 & 0x3F; // 移除命令位
    DWORD dwRegValue = pCSR->dwRegValue;

    // 检查是否为DMA地址寄存器
    if (wRegAddr >= REG_DMAADDR_1 && wRegAddr <= REG_DMAADDR_4) {
        UCHAR ucChannel = (UCHAR)((wRegAddr - REG_DMAADDR_1) / 4);
        UpdateDMAAddress(ucChannel, dwRegValue, 0);

        // 更新地址管理器的DMA上下文
        if (g_pAddressManager) {
            g_pAddressManager->SetDMAContext(GetCurrentProcessId(), dwRegValue, 0, USB_EP_DMAIN1 + ucChannel);
        }
    }

    // 发送CSR写入请求到网络
    BOOL bResult = g_pNetworkClient->WriteCSR(wRegAddr, dwRegValue, fCSR);

    if (bResult && LengthTransferred) {
        *LengthTransferred = BufferLength;
    }

    return bResult;
}

// DMA地址跟踪函数
VOID UpdateDMAAddress(UCHAR ucChannel, QWORD qwAddress, DWORD dwLength) {
    if (ucChannel >= 4) return;

    EnterCriticalSection(&g_dmaTrackerLock);

    g_dmaTracker.qwDMAAddress[ucChannel] = qwAddress;
    g_dmaTracker.dwDMALength[ucChannel] = dwLength;
    g_dmaTracker.dwLastUpdateTime[ucChannel] = GetTickCount();
    g_dmaTracker.bValid[ucChannel] = TRUE;

    LeaveCriticalSection(&g_dmaTrackerLock);
}

QWORD GetDMAAddress(UCHAR ucPipeID) {
    UCHAR ucChannel = PipeIDToChannel(ucPipeID);
    if (ucChannel >= 4) return 0;

    EnterCriticalSection(&g_dmaTrackerLock);

    QWORD qwAddress = 0;
    if (g_dmaTracker.bValid[ucChannel]) {
        qwAddress = g_dmaTracker.qwDMAAddress[ucChannel];
    }

    LeaveCriticalSection(&g_dmaTrackerLock);
    return qwAddress;
}

DWORD GetDMALength(UCHAR ucPipeID) {
    UCHAR ucChannel = PipeIDToChannel(ucPipeID);
    if (ucChannel >= 4) return 0;

    EnterCriticalSection(&g_dmaTrackerLock);

    DWORD dwLength = 0;
    if (g_dmaTracker.bValid[ucChannel]) {
        dwLength = g_dmaTracker.dwDMALength[ucChannel];
    }

    LeaveCriticalSection(&g_dmaTrackerLock);
    return dwLength;
}

UCHAR PipeIDToChannel(UCHAR ucPipeID) {
    switch (ucPipeID) {
        case USB_EP_DMAIN1: return 0;
        case USB_EP_DMAIN2: return 1;
        case USB_EP_DMAIN3: return 2;
        case USB_EP_DMAOUT: return 0; // 写入使用通道0
        default: return 0;
    }
}

// 工具函数
BOOL IsNetworkModeEnabled() {
    EnterCriticalSection(&g_networkModeLock);
    BOOL bEnabled = g_bNetworkModeEnabled;
    LeaveCriticalSection(&g_networkModeLock);
    return bEnabled;
}

VOID EnableNetworkMode(BOOL bEnable) {
    EnterCriticalSection(&g_networkModeLock);
    g_bNetworkModeEnabled = bEnable;
    LeaveCriticalSection(&g_networkModeLock);
}

DWORD GetCurrentProcessId() {
    return ::GetCurrentProcessId();
}
