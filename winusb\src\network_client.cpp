#include "../include/network_client.h"
#include <stdio.h>

#pragma comment(lib, "ws2_32.lib")

// 全局网络客户端实例
NetworkClient* g_pNetworkClient = nullptr;

NetworkClient::NetworkClient() {
    m_socket = INVALID_SOCKET;
    InitializeCriticalSection(&m_lock);
    m_bInitialized = FALSE;
    m_bConnected = FALSE;
    m_dwSequenceNumber = 0;
    
    // 默认配置
    strcpy_s(m_szServerIP, sizeof(m_szServerIP), "*************");
    m_wServerPort = 28473;
    m_dwConnectTimeout = 5000;  // 5秒
    m_dwReadTimeout = 10000;    // 10秒
    
    // 统计信息
    m_dwRequestCount = 0;
    m_dwErrorCount = 0;
    m_qwBytesTransferred = 0;
}

NetworkClient::~NetworkClient() {
    Cleanup();
    DeleteCriticalSection(&m_lock);
}

BOOL NetworkClient::Initialize(const char* szServerIP, WORD wPort) {
    EnterCriticalSection(&m_lock);
    
    if (m_bInitialized) {
        LeaveCriticalSection(&m_lock);
        return TRUE;
    }
    
    // 保存配置
    if (szServerIP) {
        strcpy_s(m_szServerIP, sizeof(m_szServerIP), szServerIP);
    }
    m_wServerPort = wPort;
    
    // 初始化Winsock
    if (!InitializeWinsock()) {
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    m_bInitialized = TRUE;
    LeaveCriticalSection(&m_lock);
    return TRUE;
}

VOID NetworkClient::Cleanup() {
    EnterCriticalSection(&m_lock);
    
    if (m_bConnected) {
        Disconnect();
    }
    
    if (m_bInitialized) {
        WSACleanup();
        m_bInitialized = FALSE;
    }
    
    LeaveCriticalSection(&m_lock);
}

BOOL NetworkClient::Connect() {
    EnterCriticalSection(&m_lock);
    
    if (m_bConnected) {
        LeaveCriticalSection(&m_lock);
        return TRUE;
    }
    
    if (!m_bInitialized) {
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    // 创建套接字
    if (!CreateSocket()) {
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    // 设置套接字选项
    if (!SetSocketOptions()) {
        CloseSocket();
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    // 连接到服务器
    if (!ConnectToServer()) {
        CloseSocket();
        LeaveCriticalSection(&m_lock);
        return FALSE;
    }
    
    m_bConnected = TRUE;
    LeaveCriticalSection(&m_lock);
    return TRUE;
}

VOID NetworkClient::Disconnect() {
    EnterCriticalSection(&m_lock);
    
    if (m_bConnected) {
        // 发送断开连接命令
        NETWORK_DMA_HEADER disconnectCmd = {0};
        disconnectCmd.dwMagic = NETWORK_DMA_MAGIC;
        disconnectCmd.dwVersion = NETWORK_DMA_VERSION;
        disconnectCmd.dwCommand = NETWORK_DMA_CMD_DISCONNECT;
        disconnectCmd.dwSequence = GetNextSequenceNumber();
        disconnectCmd.qwTimestamp = GetTickCount64();
        disconnectCmd.dwDataLength = 0;
        
        SendData(&disconnectCmd, sizeof(disconnectCmd));
        
        CloseSocket();
        m_bConnected = FALSE;
    }
    
    LeaveCriticalSection(&m_lock);
}

BOOL NetworkClient::EnsureConnection() {
    if (m_bConnected) {
        // 测试连接
        if (TestConnection()) {
            return TRUE;
        }
        
        // 连接失效，断开重连
        Disconnect();
    }
    
    return Connect();
}

BOOL NetworkClient::SendRequest(PNETWORK_DMA_HEADER pRequest, DWORD dwRequestSize) {
    if (!pRequest || dwRequestSize < sizeof(NETWORK_DMA_HEADER)) {
        return FALSE;
    }
    
    if (!EnsureConnection()) {
        UpdateStatistics(FALSE, 0);
        return FALSE;
    }
    
    // 设置校验和
    SetChecksum(pRequest, dwRequestSize);
    
    BOOL bResult = SendData(pRequest, dwRequestSize);
    UpdateStatistics(bResult, bResult ? dwRequestSize : 0);
    
    return bResult;
}

BOOL NetworkClient::ReceiveResponse(PNETWORK_DMA_HEADER pResponse, 
                                   DWORD dwMaxResponseSize, PDWORD pdwBytesReceived) {
    if (!pResponse || dwMaxResponseSize < sizeof(NETWORK_DMA_HEADER)) {
        return FALSE;
    }
    
    // 首先接收响应头
    if (!ReceiveExactly(pResponse, sizeof(NETWORK_DMA_HEADER))) {
        UpdateStatistics(FALSE, 0);
        return FALSE;
    }
    
    // 验证响应头
    if (!ValidateHeader(pResponse, 0)) {
        UpdateStatistics(FALSE, 0);
        return FALSE;
    }
    
    DWORD dwTotalSize = sizeof(NETWORK_DMA_HEADER) + pResponse->dwDataLength;
    if (dwTotalSize > dwMaxResponseSize) {
        UpdateStatistics(FALSE, 0);
        return FALSE;
    }
    
    // 接收响应数据
    if (pResponse->dwDataLength > 0) {
        PBYTE pDataBuffer = (PBYTE)pResponse + sizeof(NETWORK_DMA_HEADER);
        if (!ReceiveExactly(pDataBuffer, pResponse->dwDataLength)) {
            UpdateStatistics(FALSE, 0);
            return FALSE;
        }
    }
    
    // 验证校验和
    if (!ValidateChecksum(pResponse, dwTotalSize)) {
        UpdateStatistics(FALSE, 0);
        return FALSE;
    }
    
    if (pdwBytesReceived) {
        *pdwBytesReceived = dwTotalSize;
    }
    
    UpdateStatistics(TRUE, dwTotalSize);
    return TRUE;
}

BOOL NetworkClient::SendAndReceive(PNETWORK_DMA_HEADER pRequest, DWORD dwRequestSize, 
                                  PVOID pResponseData, DWORD dwMaxResponseSize, 
                                  PDWORD pdwBytesReceived) {
    // 发送请求
    if (!SendRequest(pRequest, dwRequestSize)) {
        return FALSE;
    }
    
    // 接收响应
    BYTE responseBuffer[8192]; // 8KB缓冲区
    DWORD dwResponseSize = min(sizeof(responseBuffer), dwMaxResponseSize + sizeof(NETWORK_DMA_HEADER));
    
    PNETWORK_DMA_HEADER pResponseHeader = (PNETWORK_DMA_HEADER)responseBuffer;
    DWORD dwBytesReceived = 0;
    
    if (!ReceiveResponse(pResponseHeader, dwResponseSize, &dwBytesReceived)) {
        return FALSE;
    }
    
    // 检查响应状态
    if (dwBytesReceived >= sizeof(NETWORK_DMA_RESPONSE)) {
        PNETWORK_DMA_RESPONSE pResponse = (PNETWORK_DMA_RESPONSE)responseBuffer;
        if (pResponse->dwStatus != NETWORK_DMA_STATUS_SUCCESS) {
            SetLastError(pResponse->dwErrorCode);
            return FALSE;
        }
    }
    
    // 复制响应数据
    if (pResponseData && pResponseHeader->dwDataLength > 0) {
        DWORD dwDataToCopy = min(pResponseHeader->dwDataLength, dwMaxResponseSize);
        memcpy(pResponseData, responseBuffer + sizeof(NETWORK_DMA_RESPONSE), dwDataToCopy);
        
        if (pdwBytesReceived) {
            *pdwBytesReceived = dwDataToCopy;
        }
    }
    
    return TRUE;
}

DWORD NetworkClient::GetNextSequenceNumber() {
    return InterlockedIncrement(&m_dwSequenceNumber);
}

BOOL NetworkClient::TestConnection() {
    NETWORK_DMA_HEADER pingRequest = {0};
    pingRequest.dwMagic = NETWORK_DMA_MAGIC;
    pingRequest.dwVersion = NETWORK_DMA_VERSION;
    pingRequest.dwCommand = NETWORK_DMA_CMD_PING;
    pingRequest.dwSequence = GetNextSequenceNumber();
    pingRequest.qwTimestamp = GetTickCount64();
    pingRequest.dwDataLength = 0;
    
    return SendAndReceive(&pingRequest, sizeof(pingRequest), nullptr, 0, nullptr);
}

VOID NetworkClient::GetStatistics(PDWORD pdwRequests, PDWORD pdwErrors, PQWORD pqwBytes) {
    EnterCriticalSection(&m_lock);

    if (pdwRequests) *pdwRequests = m_dwRequestCount;
    if (pdwErrors) *pdwErrors = m_dwErrorCount;
    if (pqwBytes) *pqwBytes = m_qwBytesTransferred;

    LeaveCriticalSection(&m_lock);
}

// 私有方法实现
BOOL NetworkClient::InitializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    return result == 0;
}

BOOL NetworkClient::CreateSocket() {
    m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    return m_socket != INVALID_SOCKET;
}

BOOL NetworkClient::SetSocketOptions() {
    // 设置接收超时
    DWORD dwTimeout = m_dwReadTimeout;
    if (setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO,
                   (char*)&dwTimeout, sizeof(dwTimeout)) == SOCKET_ERROR) {
        return FALSE;
    }

    // 设置发送超时
    dwTimeout = m_dwConnectTimeout;
    if (setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO,
                   (char*)&dwTimeout, sizeof(dwTimeout)) == SOCKET_ERROR) {
        return FALSE;
    }

    // 禁用Nagle算法以减少延迟
    BOOL bNoDelay = TRUE;
    if (setsockopt(m_socket, IPPROTO_TCP, TCP_NODELAY,
                   (char*)&bNoDelay, sizeof(bNoDelay)) == SOCKET_ERROR) {
        return FALSE;
    }

    return TRUE;
}

BOOL NetworkClient::ConnectToServer() {
    struct sockaddr_in serverAddr = {0};
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(m_wServerPort);

    if (inet_pton(AF_INET, m_szServerIP, &serverAddr.sin_addr) != 1) {
        return FALSE;
    }

    int result = connect(m_socket, (struct sockaddr*)&serverAddr, sizeof(serverAddr));
    return result != SOCKET_ERROR;
}

BOOL NetworkClient::SendData(PVOID pData, DWORD dwSize) {
    if (!pData || dwSize == 0) return FALSE;

    DWORD dwBytesSent = 0;
    PBYTE pBuffer = (PBYTE)pData;

    while (dwBytesSent < dwSize) {
        int result = send(m_socket, (char*)(pBuffer + dwBytesSent),
                         dwSize - dwBytesSent, 0);
        if (result == SOCKET_ERROR) {
            return FALSE;
        }
        dwBytesSent += result;
    }

    return TRUE;
}

BOOL NetworkClient::ReceiveData(PVOID pBuffer, DWORD dwSize) {
    if (!pBuffer || dwSize == 0) return FALSE;

    int result = recv(m_socket, (char*)pBuffer, dwSize, 0);
    return result > 0;
}

BOOL NetworkClient::ReceiveExactly(PVOID pBuffer, DWORD dwSize) {
    if (!pBuffer || dwSize == 0) return TRUE;

    DWORD dwBytesReceived = 0;
    PBYTE pBuf = (PBYTE)pBuffer;

    while (dwBytesReceived < dwSize) {
        int result = recv(m_socket, (char*)(pBuf + dwBytesReceived),
                         dwSize - dwBytesReceived, 0);
        if (result <= 0) {
            return FALSE;
        }
        dwBytesReceived += result;
    }

    return TRUE;
}

VOID NetworkClient::CloseSocket() {
    if (m_socket != INVALID_SOCKET) {
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
    }
}

VOID NetworkClient::UpdateStatistics(BOOL bSuccess, DWORD dwBytesTransferred) {
    InterlockedIncrement(&m_dwRequestCount);
    if (!bSuccess) {
        InterlockedIncrement(&m_dwErrorCount);
    }
    InterlockedAdd64((LONGLONG*)&m_qwBytesTransferred, dwBytesTransferred);
}

// 高级DMA操作实现
BOOL NetworkClient::ReadVirtualMemory(DWORD dwProcessId, QWORD qwVirtualAddress,
                                     PBYTE pBuffer, DWORD dwLength, PDWORD pdwBytesRead) {
    if (!pBuffer || dwLength == 0) return FALSE;

    NETWORK_DMA_VIRT_READ_REQUEST request;
    if (!CreateVirtualReadRequest(&request, dwProcessId, qwVirtualAddress,
                                 dwLength, USB_EP_DMAIN1, GetNextSequenceNumber())) {
        return FALSE;
    }

    return SendAndReceive(&request.header, sizeof(request),
                         pBuffer, dwLength, pdwBytesRead);
}

BOOL NetworkClient::WriteVirtualMemory(DWORD dwProcessId, QWORD qwVirtualAddress,
                                      PBYTE pBuffer, DWORD dwLength, PDWORD pdwBytesWritten) {
    if (!pBuffer || dwLength == 0) return FALSE;

    // 创建写入请求 (需要包含数据)
    DWORD dwRequestSize = sizeof(NETWORK_DMA_VIRT_WRITE_REQUEST) + dwLength;
    PBYTE pRequestBuffer = (PBYTE)malloc(dwRequestSize);
    if (!pRequestBuffer) return FALSE;

    PNETWORK_DMA_VIRT_WRITE_REQUEST pRequest = (PNETWORK_DMA_VIRT_WRITE_REQUEST)pRequestBuffer;
    ZeroMemory(pRequest, sizeof(NETWORK_DMA_VIRT_WRITE_REQUEST));

    InitializeRequestHeader(&pRequest->header, NETWORK_DMA_CMD_WRITE_VIRTUAL,
                           GetNextSequenceNumber(), dwLength);
    pRequest->dwProcessId = dwProcessId;
    pRequest->qwVirtualAddress = qwVirtualAddress;
    pRequest->dwLength = dwLength;
    pRequest->dwFlags = 0;
    pRequest->ucPipeID = USB_EP_DMAOUT;

    // 复制数据
    memcpy(pRequestBuffer + sizeof(NETWORK_DMA_VIRT_WRITE_REQUEST), pBuffer, dwLength);

    BOOL bResult = SendAndReceive(&pRequest->header, dwRequestSize,
                                 nullptr, 0, pdwBytesWritten);

    free(pRequestBuffer);
    return bResult;
}

// 全局函数实现
BOOL InitializeNetworkClient() {
    if (g_pNetworkClient) return TRUE;

    g_pNetworkClient = new NetworkClient();
    if (!g_pNetworkClient) return FALSE;

    return g_pNetworkClient->Initialize();
}

VOID CleanupNetworkClient() {
    if (g_pNetworkClient) {
        delete g_pNetworkClient;
        g_pNetworkClient = nullptr;
    }
}

// CSR操作实现
BOOL NetworkClient::WriteCSR(WORD wRegAddr, DWORD dwRegValue, BYTE fCSR) {
    NETWORK_DMA_CSR_WRITE request;
    if (!CreateCSRWriteRequest(&request, wRegAddr, dwRegValue, fCSR, GetNextSequenceNumber())) {
        return FALSE;
    }

    return SendAndReceive(&request.header, sizeof(request), nullptr, 0, nullptr);
}

BOOL NetworkClient::ReadCSR(WORD wRegAddr, PDWORD pdwRegValue, BYTE fCSR) {
    if (!pdwRegValue) return FALSE;

    NETWORK_DMA_CSR_READ request = {0};
    InitializeRequestHeader(&request.header, NETWORK_DMA_CMD_CSR_READ,
                           GetNextSequenceNumber(), 0);
    request.wRegAddr = wRegAddr;
    request.fCSR = fCSR;

    SetChecksum(&request.header, sizeof(request));

    DWORD dwBytesReceived = 0;
    return SendAndReceive(&request.header, sizeof(request),
                         pdwRegValue, sizeof(DWORD), &dwBytesReceived);
}

// Scatter读取实现
BOOL NetworkClient::ScatterReadVirtualMemory(DWORD dwProcessId, PSCATTER_ENTRY pEntries,
                                            DWORD dwEntryCount, PBYTE pBuffer, DWORD dwBufferSize) {
    if (!pEntries || dwEntryCount == 0 || !pBuffer || dwBufferSize == 0) {
        return FALSE;
    }

    // 创建scatter请求
    DWORD dwRequestSize = sizeof(NETWORK_DMA_SCATTER_REQUEST) +
                         (dwEntryCount * sizeof(SCATTER_ENTRY));
    PBYTE pRequestBuffer = (PBYTE)malloc(dwRequestSize);
    if (!pRequestBuffer) return FALSE;

    PNETWORK_DMA_SCATTER_REQUEST pRequest = (PNETWORK_DMA_SCATTER_REQUEST)pRequestBuffer;

    if (!CreateScatterReadRequest(pRequest, dwProcessId, dwEntryCount,
                                 dwBufferSize, USB_EP_DMAIN1, GetNextSequenceNumber())) {
        free(pRequestBuffer);
        return FALSE;
    }

    // 复制scatter条目
    memcpy(pRequestBuffer + sizeof(NETWORK_DMA_SCATTER_REQUEST),
           pEntries, dwEntryCount * sizeof(SCATTER_ENTRY));

    // 设置校验和
    SetChecksum(&pRequest->header, dwRequestSize);

    DWORD dwBytesReceived = 0;
    BOOL bResult = SendAndReceive(&pRequest->header, dwRequestSize,
                                 pBuffer, dwBufferSize, &dwBytesReceived);

    free(pRequestBuffer);
    return bResult;
}

// 连接状态检查
BOOL NetworkClient::IsConnected() {
    EnterCriticalSection(&m_lock);
    BOOL bConnected = m_bConnected;
    LeaveCriticalSection(&m_lock);
    return bConnected;
}
